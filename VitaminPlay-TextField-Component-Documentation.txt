# TextField Component - Vitamin Play Apple SDK

## 📋 Overview

The **TextField** component provides styled text input fields with comprehensive validation, state management, and accessibility support. It includes specialized variants for secure input (passwords) and enhanced text input with labels, helpers, and error states.

**Component Type:** `TextFieldModifier_2` with `TextFieldTheme`  
**Platform Support:** iOS 13.0+, macOS 10.15+, tvOS 13.0+, watchOS 6.0+

---

## 🎯 Key Features

- **Multiple Input Types**: TextField, SecureField, TextInput, SecureInput, PasswordInput
- **Theme Support**: Full Legacy and Wonder theme integration
- **State Management**: Normal, Focus, Error, Success states
- **Validation System**: Built-in validation with error messaging
- **Icon Support**: Start and end icon positioning with actions
- **Label & Helper Text**: Optional labels, required indicators, helper text
- **Accessibility**: VoiceOver support and keyboard navigation
- **Form Integration**: Question mark icons with help actions

---

## 🔧 Basic Usage

### Simple TextField
````swift
@State private var text = ""
@State private var isFocused = false
@State private var validation = ValidationConfiguration()

TextField("Enter your name", text: $text)
  .textFieldStyle(
    generalTheme: .wonder,
    variant: .regular,
    text: $text,
    isFocused: $isFocused,
    validationConfiguration: $validation
  )
````

### TextInput with Label and Helper
````swift
TextInput(
  generalTheme: .legacy,
  variant: .regular,
  placeholder: "Enter email address",
  text: $email,
  title: "Email Address",
  helper: "We'll never share your email",
  keyboardType: .emailAddress
)
````

### SecureInput for Passwords
````swift
SecureInput(
  generalTheme: .wonder,
  variant: .regular,
  placeholder: "Enter password",
  text: $password,
  title: "Password",
  helper: "Must be at least 8 characters"
)
````

---

## ⚙️ Configuration Options

### **Variants**
- **Regular**: Standard text field styling (only variant available)

### **States** 
- **Normal**: Default state • 1px border
- **Focus**: Active input state • 2px border • brand color
- **Error**: Validation error state • red border and text
- **Success**: Validation success state • green border and checkmark
- **Disabled**: Non-interactive state • 38% opacity

### **Input Types**
- **TextField**: Basic SwiftUI TextField with styling
- **SecureField**: Basic SwiftUI SecureField with styling  
- **TextInput**: Enhanced input with labels and validation
- **SecureInput**: Enhanced secure input with show/hide toggle
- **PasswordInput**: Specialized password input with visibility controls

---

## 🎨 Theme Examples

### Legacy Theme TextField
````swift
// Basic Legacy TextField
TextField("Username", text: $username)
  .textFieldStyle(
    generalTheme: .legacy,
    variant: .regular,
    text: $username,
    isFocused: $isFocused,
    validationConfiguration: $validation
  )

// Legacy TextInput with full features
TextInput(
  generalTheme: .legacy,
  variant: .regular,
  placeholder: "Enter your message",
  text: $message,
  title: "Message",
  titleRequired: "*",
  helper: "Maximum 500 characters",
  startIcon: Image(systemName: "envelope")
)
````

### Wonder Theme TextField
````swift
// Wonder SecureInput
SecureInput(
  generalTheme: .wonder,
  variant: .regular,
  placeholder: "Create password",
  text: $newPassword,
  title: "New Password",
  helper: "Use 8+ characters with letters and numbers"
)

// Wonder TextInput with validation
TextInput(
  generalTheme: .wonder,
  variant: .regular,
  placeholder: "Enter phone number",
  text: $phone,
  isFocused: $phoneFocused,
  validationConfiguration: $phoneValidation,
  keyboardType: .phonePad,
  title: "Phone Number",
  endIcon: Image(systemName: "phone")
)
````

---

## 🔄 Advanced Usage

### Custom Validation
````swift
@State private var validation = ValidationConfiguration(
  isValid: false,
  errorMessage: "Please enter a valid email address"
)

TextField("Email", text: $email)
  .textFieldStyle(
    generalTheme: .wonder,
    variant: .regular,
    text: $email,
    isFocused: $isFocused,
    validationConfiguration: $validation
  )
  .onChange(of: email) { newValue in
    validation.isValid = newValue.contains("@")
    validation.errorMessage = validation.isValid ? "" : "Invalid email format"
  }
````

### TextField with Icons and Actions
````swift
TextInput(
  generalTheme: .legacy,
  variant: .regular,
  placeholder: "Search products...",
  text: $searchText,
  startIcon: Image(systemName: "magnifyingglass"),
  startIconAction: { performSearch() },
  endIcon: Image(systemName: "xmark.circle.fill"),
  endIconAction: { searchText = "" }
)
````

### Password Input with Visibility Toggle
````swift
PasswordInput(
  generalTheme: .wonder,
  variant: .regular,
  placeholder: "Enter password",
  password: $password,
  title: "Password",
  startIconWithPasswordVisible: Image(systemName: "lock.open"),
  startIconWithPasswordHidden: Image(systemName: "lock"),
  endIconWithPasswordVisible: Image(systemName: "eye.slash"),
  endIconWithPasswordHidden: Image(systemName: "eye")
)
````

---

## 📐 Component Structure

### TextFieldTheme Properties
- **labelForm**: LabelFormTheme (title, required indicator, question mark)
- **textInput**: TextInputTheme (main input field styling)
- **helperForm**: HelperFormTheme (helper text styling)
- **errorForm**: ErrorFormTheme (error message styling)

### TextInputTheme Properties
- **cornerRadius**: VitaminCornerRadii (8px Legacy / 0px Wonder)
- **borderWidth**: State-based (1px normal, 2px focus)
- **backgroundColor**: Container background color
- **borderColor**: State-based border colors
- **cursorColor**: Text cursor color (brand/error)
- **foregroundColor**: Text color
- **checkmarkColor**: Success state checkmark
- **iconsColor**: Start/end icon colors
- **placeholderColor**: Placeholder text color
- **opacityDisabled**: 38% when disabled
- **minSize**: 24px minimum height
- **padding**: 16px all sides
- **gap**: 8px icon spacing
- **typography**: Body M (14px) for text and placeholder

### TextFieldStatus Enum
- **normal**: Default state
- **focus**: Active input state
- **error**: Validation error state
- **success**: Validation success state

---

*This documentation covers the TextField component. For complete theme specifications, see the Foundation Core and Semantic Design Token documentation.*
