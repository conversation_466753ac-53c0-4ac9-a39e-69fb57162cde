# Vitamin Play Apple SDK - Foundation Core Cheatsheet

## Overview
The Foundation Core contains the fundamental design tokens that form the base of the Vitamin Play design system. These tokens provide consistent values for spacing, colors, typography, and other foundational elements across all components.

## 🎯 Aspect Ratio

Predefined aspect ratios for consistent media and container proportions.

### Available Ratios
```swift
// Square ratios
DesignTokens.Core.AspectRatio.oneByOne        // 1.0/1.0 = 1.0

// Portrait ratios  
DesignTokens.Core.AspectRatio.fourByFive      // 4.0/5.0 = 0.8
DesignTokens.Core.AspectRatio.threeByFour     // 3.0/4.0 = 0.75
DesignTokens.Core.AspectRatio.nineBySixteen   // 9.0/16.0 = 0.5625
DesignTokens.Core.AspectRatio.oneByTwo        // 1.0/2.0 = 0.5
DesignTokens.Core.AspectRatio.twoByThree      // 2.0/3.0 = 0.667
DesignTokens.Core.AspectRatio.tenBySixteen    // 10.0/16.0 = 0.625

// Landscape ratios
DesignTokens.Core.AspectRatio.fiveByFour      // 5.0/4.0 = 1.25
DesignTokens.Core.AspectRatio.fourByThree     // 4.0/3.0 = 1.333
DesignTokens.Core.AspectRatio.threeByTwo      // 3.0/2.0 = 1.5
DesignTokens.Core.AspectRatio.golden          // 1.618/1.0 = 1.618
DesignTokens.Core.AspectRatio.sixteenByTen    // 16.0/10.0 = 1.6
DesignTokens.Core.AspectRatio.sixteenByNine   // 16.0/9.0 = 1.778
DesignTokens.Core.AspectRatio.twoByOne        // 2.0/1.0 = 2.0

// Ultra-wide ratios
DesignTokens.Core.AspectRatio.twentyOneByNine // 21.0/9.0 = 2.333
DesignTokens.Core.AspectRatio.nineByTwentyOne // 9.0/21.0 = 0.429
```

### Usage Example
```swift
Image("product-image")
  .resizable()
  .aspectRatio(.oneByOne, contentMode: .fit)
  .frame(width: 300, height: 200)
```

## 🔲 Border Radius

Consistent corner radius values for UI elements.

### Available Values
```swift
DesignTokens.Core.BorderRadius._0     // 0px
DesignTokens.Core.BorderRadius._050   // 2px
DesignTokens.Core.BorderRadius._100   // 4px
DesignTokens.Core.BorderRadius._200   // 8px
DesignTokens.Core.BorderRadius._300   // 12px
DesignTokens.Core.BorderRadius._400   // 16px
DesignTokens.Core.BorderRadius._500   // 20px
DesignTokens.Core.BorderRadius._600   // 28px
DesignTokens.Core.BorderRadius.full   // 999px (fully rounded)
```

### Usage Example
```swift
// Using VitaminCornerRadii for complex corner configurations
VitaminCornerRadii(all: DesignTokens.Core.BorderRadius._200)

// Or individual corners
VitaminCornerRadii(
  topLeading: DesignTokens.Core.BorderRadius._100,
  bottomLeading: DesignTokens.Core.BorderRadius._200,
  bottomTrailing: DesignTokens.Core.BorderRadius._200,
  topTrailing: DesignTokens.Core.BorderRadius._100
)
```

## 📏 Border Width

Standard border thickness values.

### Available Values
```swift
DesignTokens.Core.BorderWidth._0     // 0px (no border)
DesignTokens.Core.BorderWidth._100   // 1px
DesignTokens.Core.BorderWidth._200   // 2px
DesignTokens.Core.BorderWidth._300   // 3px
```

## 🎨 Colors

Comprehensive color palette organized by color families.

### Core Color Families
```swift
// Neutral colors
DesignTokens.Core.Color.black
DesignTokens.Core.Color.white
DesignTokens.Core.Color.transparent

// Grey scale (050-950)
DesignTokens.Core.Color.grey050    // Lightest grey
DesignTokens.Core.Color.grey100
DesignTokens.Core.Color.grey200
// ... continues to ...
DesignTokens.Core.Color.grey950    // Darkest grey

// Blue family (050-700)
DesignTokens.Core.Color.blue050    // Lightest blue
DesignTokens.Core.Color.blue100
// ... continues to ...
DesignTokens.Core.Color.blue700    // Darkest blue

// Cobalt family (010-900)
DesignTokens.Core.Color.cobalt010  // Lightest cobalt
// ... continues to ...
DesignTokens.Core.Color.cobalt900  // Darkest cobalt

// Additional color families available:
// - Conifer (green tones)
// - Mint (mint green tones)  
// - Orange family
// - Rock (neutral tones)
// - Yellow family
```

### Usage Example
```swift
Rectangle()
  .fill(DesignTokens.Core.Color.blue500)
  .frame(width: 200, height: 100)
```

## 🔤 Typography

### Font Families
```swift
DesignTokens.Core.FontFamily.decathlon      // "Decathlon VF"
DesignTokens.Core.FontFamily.decathlonBrand // "Decathlon Brand VF"
DesignTokens.Core.FontFamily.inter          // "Inter"
DesignTokens.Core.FontFamily.mono           // "monospace"
DesignTokens.Core.FontFamily.roboto         // "Roboto"
DesignTokens.Core.FontFamily.sansSerif      // "sans-serif"
DesignTokens.Core.FontFamily.sfPro          // "SF Pro"
```

### Font Sizes
```swift
DesignTokens.Core.FontSize._050   // 11px
DesignTokens.Core.FontSize._100   // 12px
DesignTokens.Core.FontSize._150   // 14px
DesignTokens.Core.FontSize._200   // 16px
DesignTokens.Core.FontSize._250   // 18px
DesignTokens.Core.FontSize._300   // 20px
DesignTokens.Core.FontSize._350   // 22px
DesignTokens.Core.FontSize._400   // 24px
// ... continues up to ...
DesignTokens.Core.FontSize._950   // 88px
```

### Font Weights
```swift
DesignTokens.Core.FontWeight._100   // 100 (Thin)
DesignTokens.Core.FontWeight._200   // 200 (Extra Light)
DesignTokens.Core.FontWeight._300   // 300 (Light)
DesignTokens.Core.FontWeight._400   // 400 (Regular)
DesignTokens.Core.FontWeight._500   // 500 (Medium)
DesignTokens.Core.FontWeight._600   // 600 (Semi Bold)
DesignTokens.Core.FontWeight._700   // 700 (Bold)
DesignTokens.Core.FontWeight._800   // 800 (Extra Bold)
DesignTokens.Core.FontWeight._900   // 900 (Black)
DesignTokens.Core.FontWeight._950   // 950 (Extra Black)
```

### Letter Spacing
```swift
DesignTokens.Core.LetterSpacing._020   // -3px (tighter)
DesignTokens.Core.LetterSpacing._050   // -1px
DesignTokens.Core.LetterSpacing._100   // 0px (normal)
DesignTokens.Core.LetterSpacing._200   // 1px
DesignTokens.Core.LetterSpacing._300   // 2px (looser)
```

### Line Height
```swift
DesignTokens.Core.LineHeight._050   // 100.0%
DesignTokens.Core.LineHeight._100   // 107.14%
DesignTokens.Core.LineHeight._200   // 110.0%
// ... continues up to ...
DesignTokens.Core.LineHeight._900   // 170.0%
```

## 📐 Sizing

Standard sizing values for consistent component dimensions.

### Available Sizes
```swift
DesignTokens.Core.Sizing._050   // 1px
DesignTokens.Core.Sizing._100   // 2px
DesignTokens.Core.Sizing._150   // 4px
DesignTokens.Core.Sizing._200   // 8px
DesignTokens.Core.Sizing._250   // 12px
DesignTokens.Core.Sizing._300   // 16px
DesignTokens.Core.Sizing._325   // 20px
DesignTokens.Core.Sizing._350   // 24px
DesignTokens.Core.Sizing._400   // 32px
DesignTokens.Core.Sizing._450   // 40px
DesignTokens.Core.Sizing._475   // 44px (accessibility touch size)
DesignTokens.Core.Sizing._500   // 48px
DesignTokens.Core.Sizing._520   // 56px
DesignTokens.Core.Sizing._550   // 64px
DesignTokens.Core.Sizing._600   // 80px
DesignTokens.Core.Sizing._650   // 96px
DesignTokens.Core.Sizing._700   // 128px
```

## 📏 Spacing

Consistent spacing values for margins, padding, and gaps.

### Available Spacing
```swift
DesignTokens.Core.Spacing._0     // 0px
DesignTokens.Core.Spacing._010   // 1px
DesignTokens.Core.Spacing._025   // 2px
DesignTokens.Core.Spacing._050   // 4px
DesignTokens.Core.Spacing._075   // 6px
DesignTokens.Core.Spacing._100   // 8px
DesignTokens.Core.Spacing._150   // 12px
DesignTokens.Core.Spacing._200   // 16px
DesignTokens.Core.Spacing._250   // 20px
DesignTokens.Core.Spacing._300   // 24px
DesignTokens.Core.Spacing._400   // 32px
DesignTokens.Core.Spacing._500   // 40px
DesignTokens.Core.Spacing._600   // 48px
DesignTokens.Core.Spacing._650   // 64px
DesignTokens.Core.Spacing._700   // 80px
DesignTokens.Core.Spacing._800   // 96px
DesignTokens.Core.Spacing._850   // 128px
DesignTokens.Core.Spacing._900   // 160px
```

### Usage Example
```swift
VStack(spacing: CGFloat(DesignTokens.Core.Spacing._200)) {
  Text("Title")
  Text("Description")
}
.padding(CGFloat(DesignTokens.Core.Spacing._300))
```

## 🔍 Opacity

Standard opacity values for consistent transparency effects.

### Available Values
```swift
DesignTokens.Core.Opacity._0      // 0% (fully transparent)
DesignTokens.Core.Opacity._250    // 25%
DesignTokens.Core.Opacity._300    // 38%
DesignTokens.Core.Opacity._500    // 50%
DesignTokens.Core.Opacity._750    // 75%
DesignTokens.Core.Opacity._1000   // 100% (fully opaque)
```

## 📝 Text Styling

### Text Case
```swift
DesignTokens.Core.TextCase.none         // "none"
DesignTokens.Core.TextCase.capitalize   // "capitalize"
DesignTokens.Core.TextCase.lowercase    // "lowercase"
DesignTokens.Core.TextCase.uppercase    // "uppercase"
```

### Text Decoration
```swift
DesignTokens.Core.TextDecoration.none         // "none"
DesignTokens.Core.TextDecoration.underline    // "underline"
DesignTokens.Core.TextDecoration.lineThrough  // "line-through"
```

### Optical Sizing
```swift
DesignTokens.Core.OpticalSizing.auto   // "auto"
DesignTokens.Core.OpticalSizing._14    // "14"
DesignTokens.Core.OpticalSizing._30    // "30"
```

---

## 💡 Usage Tips

1. **Consistency**: Always use design tokens instead of hardcoded values
2. **Accessibility**: The `_475` sizing token represents the minimum touch target size (44px)
3. **Semantic Usage**: Consider using semantic tokens (Legacy/Wonder) for component-specific styling
4. **Theme Support**: All color tokens automatically adapt to the current theme configuration

## 🔗 Next Steps

- [Legacy Semantics Cheatsheet](VitaminPlay-Legacy-Semantics-Cheatsheet.md)
- [Wonder Semantics Cheatsheet](VitaminPlay-Wonder-Semantics-Cheatsheet.md)
- [Component Usage Guide](VitaminPlay-Components-Cheatsheet.md)
