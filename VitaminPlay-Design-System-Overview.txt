# Vitamin Play Apple SDK - Design System Documentation

Welcome to the comprehensive design system documentation for the **Vitamin Play Apple SDK**. This documentation provides complete reference materials for implementing consistent, accessible, and beautiful user interfaces using our design token system.

---

## 🎯 What is Vitamin Play?

Vitamin Play is Decathlon's design system for Apple platforms, providing a comprehensive set of design tokens, semantic values, and components that ensure consistency across all iOS and macOS applications. The system follows a **three-tier architecture**:

```
Foundation Core → Semantic Tokens → UI Components
```

---

## 📚 Documentation Structure

This documentation is organized into **three comprehensive sections**, each serving a specific purpose in your design and development workflow:

### 1. 🏗️ Foundation Core Design Tokens
**The building blocks of the design system**

Foundation Core contains **241 design tokens** across **15 categories** that serve as the atomic values for all design decisions. These are the raw, numerical values that power the entire system.

**What you'll find:**
- **15 token categories**: AspectRatio, BorderRadius, BorderWidth, Colors, Elevation, FontFamily, FontSize, FontWeight, LetterSpacing, LineHeight, Motion, OpticalSizing, Sizing, Spacing, TextCase
- **241 individual tokens**: From basic colors to complex motion curves
- **SwiftUI integration**: Direct usage in your iOS/macOS apps
- **Pixel-perfect specifications**: Exact values for design handoff

**Best for:**
- Developers implementing custom components
- Designers creating new design patterns
- Understanding the foundational system architecture

---

### 2. 🎨 Legacy Semantics Design Tokens
**Context-aware tokens for the Legacy theme**

Legacy Semantics provide **meaningful, context-aware design tokens** that map Foundation Core values to semantic names. This theme uses **Roboto** as the primary font family and emphasizes **rounded, friendly design**.

**What you'll find:**
- **6 semantic categories**: BorderRadius, BorderWidth, Colors, Sizing, Spacing, Typography
- **44 semantic colors**: Organized by purpose (Alpha, Background, Border, Container, Content, Status)
- **13 typography styles**: Complete Figma specifications with Roboto font
- **Accessibility support**: 44px touch targets and semantic naming
- **Rounded design**: 4px default border radius for friendly aesthetics

**Typography highlights:**
- **Font Family**: Roboto with auto optical sizing
- **Complete styles**: Body, Button, Display, Title variations
- **Figma-ready specs**: Font weight, size, line height, letter spacing

**Best for:**
- Projects using the traditional Decathlon design language
- Applications requiring rounded, approachable aesthetics
- Teams familiar with Roboto typography

---

### 3. ✨ Wonder Semantics Design Tokens
**Modern tokens for the Wonder theme**

Wonder Semantics represent the **evolved, contemporary design approach** with **Decathlon** as the primary font family and **sharp, angular design elements** for a more modern aesthetic.

**What you'll find:**
- **6 semantic categories**: BorderRadius, BorderWidth, Colors, Sizing, Spacing, Typography
- **44 semantic colors**: Wonder-optimized color values
- **11 typography styles**: Complete Figma specifications with Decathlon font
- **Extended sizing**: 3 additional larger sizes (80px, 96px, 128px)
- **Sharp design**: 0px default border radius for modern aesthetics

**Typography highlights:**
- **Font Family**: Decathlon with specific optical sizing (14, 30)
- **Special fonts**: Decathlon Brand for inspiring titles
- **Modern styles**: Body, Button Labels, Titles, Subtitles, Special styles
- **Contemporary sizing**: Optimized for modern design patterns

**Key differences from Legacy:**
- **Sharp corners**: 0px main border radius vs 4px
- **Decathlon font**: Modern typography vs Roboto
- **Extended sizing**: More layout flexibility
- **Specific optical sizing**: Better font rendering control

**Best for:**
- New projects embracing modern design language
- Applications requiring sharp, contemporary aesthetics
- Teams adopting the latest Decathlon brand evolution

---

## 🔄 Design Token Architecture

### Three-Tier System
```
1. Foundation Core (Raw Values)
   ↓
2. Semantic Tokens (Contextual Meaning)
   ↓
3. UI Components (Implementation)
```

### Usage Pattern
```swift
// Foundation Core (raw value)
DesignTokens.Core.BorderRadius._100 // 4px

// Semantic Token (contextual meaning)
DesignTokens.SemanticLegacy.BorderRadius.main // 4px
DesignTokens.SemanticWonder.BorderRadius.main // 0px

// Component Usage (implementation)
RoundedRectangle(cornerRadius: CGFloat(DesignTokens.SemanticLegacy.BorderRadius.main))
```

---

## 🎨 Theme Comparison

| Aspect | Legacy Theme | Wonder Theme |
|--------|-------------|--------------|
| **Font Family** | Roboto | Decathlon |
| **Design Style** | Rounded, Friendly | Sharp, Modern |
| **Main Border Radius** | 4px | 0px (Sharp) |
| **Optical Sizing** | Auto | Specific (14, 30) |
| **Color Approach** | Traditional | Contemporary |
| **Sizing Options** | 13 values | 16 values (3 additional) |
| **Typography Styles** | 13 styles | 11 styles |
| **Special Features** | Display typography | Decathlon Brand font |

---

## 🚀 Getting Started

### For Designers
1. **Start with Foundation Core** to understand the system architecture
2. **Choose your theme** (Legacy for traditional, Wonder for modern)
3. **Reference typography specs** for accurate Figma implementation
4. **Use semantic color names** for consistent design decisions

### For Developers
1. **Import the SDK** into your Xcode project
2. **Choose semantic tokens** over Foundation Core for flexibility
3. **Follow the three-tier architecture** for maintainable code
4. **Test accessibility** with provided touch size standards

### Code Examples
```swift
// Typography
Text("Hello World")
  .font(DesignTokens.SemanticWonder.Typography.mobileTitleM.font)
  .foregroundColor(DesignTokens.SemanticWonder.Color.contentBrand)

// Layout
VStack(spacing: CGFloat(DesignTokens.SemanticWonder.Spacing.xl)) {
  // Content
}
.padding(CGFloat(DesignTokens.SemanticWonder.Spacing.xxxl))

// Shapes
RoundedRectangle(cornerRadius: CGFloat(DesignTokens.SemanticWonder.BorderRadius.main))
  .fill(DesignTokens.SemanticWonder.Color.containerBrand)
```

---

## 📖 Navigation Guide

**Ready to dive deeper?** Each section provides comprehensive documentation:

### 🏗️ Foundation Core Design Tokens
Complete reference for all 241 foundation tokens across 15 categories with exact specifications and usage examples.

### 🎨 Legacy Semantics Design Tokens  
Detailed documentation for the Roboto-based Legacy theme with complete typography breakdowns and Figma specifications.

### ✨ Wonder Semantics Design Tokens
Comprehensive guide to the Decathlon-based Wonder theme with modern design patterns and extended sizing options.

---

## 💡 Best Practices

### Design Consistency
- **Use semantic tokens** instead of Foundation Core for better maintainability
- **Choose one theme** per project for consistency
- **Follow accessibility guidelines** with provided touch size standards
- **Reference typography specs** for accurate implementation

### Development Efficiency
- **Leverage SwiftUI integration** for seamless implementation
- **Use meaningful semantic names** for self-documenting code
- **Test across themes** if supporting multiple design languages
- **Follow the three-tier architecture** for scalable solutions

### Team Collaboration
- **Share semantic token names** between design and development
- **Use Figma specifications** for accurate design handoff
- **Document theme choices** in project requirements
- **Maintain consistency** across all touchpoints

---

## 🔗 Quick Links

- **Foundation Core Documentation**: Complete reference for all 241 design tokens
- **Legacy Semantics Documentation**: Roboto-based theme with rounded design
- **Wonder Semantics Documentation**: Decathlon-based theme with modern aesthetics

---

*This documentation is maintained by the Vitamin Play team and reflects the current state of the design system. For questions or contributions, please reach out to the design system team.*
