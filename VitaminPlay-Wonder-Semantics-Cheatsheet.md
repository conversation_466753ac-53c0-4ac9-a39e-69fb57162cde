# Vitamin Play Apple SDK - Wonder Semantics Cheatsheet

## Overview
Wonder Semantics provide the modern, evolved design tokens for the Wonder theme. These tokens emphasize a more contemporary design approach with **Decathlon** as the primary font family and **sharper, more angular** design elements.

## 🔲 Border Radius

Wonder theme uses more angular design with different radius values than Legacy.

### Available Values
```swift
DesignTokens.SemanticWonder.BorderRadius.main  // Core._0 = 0px (sharp corners!)
DesignTokens.SemanticWonder.BorderRadius.s     // Core._200 = 8px  
DesignTokens.SemanticWonder.BorderRadius.m     // Core._500 = 20px (larger than Legacy)
DesignTokens.SemanticWonder.BorderRadius.full  // Core.full = 999px
```

### Key Differences from Legacy
- **`main`**: 0px (sharp) vs Legacy's 4px (rounded)
- **`m`**: 20px vs Legacy's 16px (more pronounced rounding)

### Usage Example
```swift
RoundedRectangle(cornerRadius: CGFloat(DesignTokens.SemanticWonder.BorderRadius.main))
  .fill(Color.blue) // Creates sharp-cornered rectangle
```

## 📏 Border Width

Identical to Legacy semantic values.

### Available Values
```swift
DesignTokens.SemanticWonder.BorderWidth.none  // Core._0 = 0px
DesignTokens.SemanticWonder.BorderWidth.s     // Core._100 = 1px
DesignTokens.SemanticWonder.BorderWidth.m     // Core._200 = 2px
DesignTokens.SemanticWonder.BorderWidth.l     // Core._300 = 3px
```

## 🎨 Colors

Wonder theme colors with the same semantic structure as Legacy but different visual values.

### Alpha Colors (Transparency)
```swift
// Brand alpha colors
DesignTokens.SemanticWonder.Color.alphaBrandL    // Large brand transparency
DesignTokens.SemanticWonder.Color.alphaBrandM    // Medium brand transparency
DesignTokens.SemanticWonder.Color.alphaBrandS    // Small brand transparency

// Neutral alpha colors
DesignTokens.SemanticWonder.Color.alphaNeutralL   // Large neutral transparency
DesignTokens.SemanticWonder.Color.alphaNeutralM   // Medium neutral transparency
DesignTokens.SemanticWonder.Color.alphaNeutralS   // Small neutral transparency
DesignTokens.SemanticWonder.Color.alphaNeutralXs  // Extra small neutral transparency

// Quiet alpha colors
DesignTokens.SemanticWonder.Color.alphaQuietM     // Medium quiet transparency
DesignTokens.SemanticWonder.Color.alphaQuietS     // Small quiet transparency
```

### Background Colors
```swift
DesignTokens.SemanticWonder.Color.backgroundMain         // Primary background
DesignTokens.SemanticWonder.Color.backgroundAlternative  // Alternative background
```

### Border Colors
```swift
DesignTokens.SemanticWonder.Color.borderBrand     // Brand border color
DesignTokens.SemanticWonder.Color.borderCatchy    // Attention-grabbing border
DesignTokens.SemanticWonder.Color.borderInverse   // Inverse border color
DesignTokens.SemanticWonder.Color.borderNeutral   // Neutral border color
DesignTokens.SemanticWonder.Color.borderOnBrand   // Border on brand backgrounds
DesignTokens.SemanticWonder.Color.borderQuiet     // Subtle border color
```

### Container Colors
```swift
DesignTokens.SemanticWonder.Color.containerBrand              // Brand container
DesignTokens.SemanticWonder.Color.containerCatchy             // Catchy container
DesignTokens.SemanticWonder.Color.containerCommercialCatchy   // Commercial catchy
DesignTokens.SemanticWonder.Color.containerCommercialNeutral  // Commercial neutral
DesignTokens.SemanticWonder.Color.containerNeutral            // Neutral container
DesignTokens.SemanticWonder.Color.containerOnBrand            // Container on brand
DesignTokens.SemanticWonder.Color.containerOnOverlay          // Container on overlay
DesignTokens.SemanticWonder.Color.containerOverlay            // Overlay container
DesignTokens.SemanticWonder.Color.containerQuiet              // Quiet container
```

### Content Colors
```swift
DesignTokens.SemanticWonder.Color.contentBrand                // Brand content color
DesignTokens.SemanticWonder.Color.contentInverse              // Inverse content
DesignTokens.SemanticWonder.Color.contentLogo                 // Logo content
DesignTokens.SemanticWonder.Color.contentNeutral              // Neutral content
DesignTokens.SemanticWonder.Color.contentOnBrandAccent        // Accent on brand
DesignTokens.SemanticWonder.Color.contentOnBrandNeutral       // Neutral on brand
DesignTokens.SemanticWonder.Color.contentOnBrandQuiet         // Quiet on brand
DesignTokens.SemanticWonder.Color.contentOnCommercialCatchy   // Content on commercial catchy
DesignTokens.SemanticWonder.Color.contentOnCommercialNeutral  // Content on commercial neutral
DesignTokens.SemanticWonder.Color.contentOnMediaAccent        // Accent on media
DesignTokens.SemanticWonder.Color.contentOnMediaNeutral       // Neutral on media
DesignTokens.SemanticWonder.Color.contentQuiet                // Quiet content
DesignTokens.SemanticWonder.Color.contentService              // Service content
```

### Status Colors
```swift
DesignTokens.SemanticWonder.Color.statusInfo       // Information status
DesignTokens.SemanticWonder.Color.statusNegative   // Error/negative status
DesignTokens.SemanticWonder.Color.statusPositive   // Success/positive status
DesignTokens.SemanticWonder.Color.statusWarning    // Warning status
```

### Usage Example
```swift
VStack {
  Text("Wonder Brand Content")
    .foregroundColor(DesignTokens.SemanticWonder.Color.contentBrand)
  Text("Wonder Quiet Content")
    .foregroundColor(DesignTokens.SemanticWonder.Color.contentQuiet)
}
.padding()
.background(DesignTokens.SemanticWonder.Color.containerNeutral)
```

## 🎬 Motion

Animation system with subtle differences from Legacy.

### Delays & Durations
```swift
// Delays (identical to Legacy)
DesignTokens.SemanticWonder.Motion.delayInstant   // 0.0s
DesignTokens.SemanticWonder.Motion.delayQuick     // 0.07s
DesignTokens.SemanticWonder.Motion.delayModerate  // 0.17s
DesignTokens.SemanticWonder.Motion.delaySlow      // 0.33s
DesignTokens.SemanticWonder.Motion.delayExtended  // 0.6s

// Durations (identical to Legacy)
DesignTokens.SemanticWonder.Motion.durationInstant   // 0.0s
DesignTokens.SemanticWonder.Motion.durationQuick     // 0.25s
DesignTokens.SemanticWonder.Motion.durationModerate  // 0.6s
DesignTokens.SemanticWonder.Motion.durationSlow      // 1.0s
DesignTokens.SemanticWonder.Motion.durationExtended  // 1.3s
```

### Easing Functions
```swift
// Expressive easing (slightly different curves than Legacy)
DesignTokens.SemanticWonder.Motion.easingExpressiveLinear
DesignTokens.SemanticWonder.Motion.easingExpressiveEaseIn
DesignTokens.SemanticWonder.Motion.easingExpressiveEaseOut
DesignTokens.SemanticWonder.Motion.easingExpressiveEaseInOut  // Different curve: y2: 0.75

// Functional easing (identical to Legacy)
DesignTokens.SemanticWonder.Motion.easingFunctionalLinear
DesignTokens.SemanticWonder.Motion.easingFunctionalEaseIn
DesignTokens.SemanticWonder.Motion.easingFunctionalEaseOut
DesignTokens.SemanticWonder.Motion.easingFunctionalEaseInOut
```

## 🔍 Opacity

Identical semantic opacity values to Legacy.

### Available Values
```swift
DesignTokens.SemanticWonder.Opacity.none  // Core._0 = 0%
DesignTokens.SemanticWonder.Opacity.xs    // Core._250 = 25%
DesignTokens.SemanticWonder.Opacity.s     // Core._300 = 38%
DesignTokens.SemanticWonder.Opacity.m     // Core._500 = 50%
DesignTokens.SemanticWonder.Opacity.l     // Core._750 = 75%
DesignTokens.SemanticWonder.Opacity.full  // Core._1000 = 100%
```

## 📐 Sizing

Extended sizing scale compared to Legacy.

### Available Sizes
```swift
DesignTokens.SemanticWonder.Sizing.accessibilityTouchSize  // Core._475 = 44px
DesignTokens.SemanticWonder.Sizing.xxxs                    // Core._050 = 1px
DesignTokens.SemanticWonder.Sizing.xxs                     // Core._150 = 4px
DesignTokens.SemanticWonder.Sizing.xs                      // Core._200 = 8px
DesignTokens.SemanticWonder.Sizing.s                       // Core._250 = 12px
DesignTokens.SemanticWonder.Sizing.m                       // Core._300 = 16px
DesignTokens.SemanticWonder.Sizing.l                       // Core._325 = 20px
DesignTokens.SemanticWonder.Sizing.xl                      // Core._350 = 24px
DesignTokens.SemanticWonder.Sizing.xxl                     // Core._400 = 32px
DesignTokens.SemanticWonder.Sizing.xxxl                    // Core._450 = 40px
DesignTokens.SemanticWonder.Sizing.xxxxl                   // Core._500 = 48px
DesignTokens.SemanticWonder.Sizing.xxxxxl                  // Core._520 = 56px
DesignTokens.SemanticWonder.Sizing.xxxxxxl                 // Core._550 = 64px
DesignTokens.SemanticWonder.Sizing.xxxxxxxl                // Core._600 = 80px
DesignTokens.SemanticWonder.Sizing.xxxxxxxxl               // Core._650 = 96px
DesignTokens.SemanticWonder.Sizing.xxxxxxxxxl              // Core._700 = 128px
```

### Key Differences from Legacy
Wonder includes **3 additional larger sizes** (xxxxxxxl, xxxxxxxxl, xxxxxxxxxl) for more flexible layouts.

## 📏 Spacing

Identical semantic spacing values to Legacy.

### Available Spacing
```swift
DesignTokens.SemanticWonder.Spacing.xxxxs      // Core._0 = 0px
DesignTokens.SemanticWonder.Spacing.xxxs       // Core._025 = 2px
DesignTokens.SemanticWonder.Spacing.xxs        // Core._050 = 4px
DesignTokens.SemanticWonder.Spacing.xs         // Core._100 = 8px
DesignTokens.SemanticWonder.Spacing.s          // Core._150 = 12px
DesignTokens.SemanticWonder.Spacing.m          // Core._200 = 16px
DesignTokens.SemanticWonder.Spacing.l          // Core._250 = 20px
DesignTokens.SemanticWonder.Spacing.xl         // Core._300 = 24px
DesignTokens.SemanticWonder.Spacing.xxl        // Core._400 = 32px
DesignTokens.SemanticWonder.Spacing.xxxl       // Core._500 = 40px
DesignTokens.SemanticWonder.Spacing.xxxxl      // Core._600 = 48px
DesignTokens.SemanticWonder.Spacing.xxxxxl     // Core._650 = 64px
DesignTokens.SemanticWonder.Spacing.xxxxxxl    // Core._700 = 80px
DesignTokens.SemanticWonder.Spacing.xxxxxxxl   // Core._800 = 96px
DesignTokens.SemanticWonder.Spacing.xxxxxxxxl  // Core._850 = 128px
DesignTokens.SemanticWonder.Spacing.xxxxxxxxxl // Core._900 = 160px
```

## 🔤 Typography

Wonder typography system using **Decathlon** font family with modern styling.

### Body Text
```swift
// Large body text: Decathlon Medium 20px, Line Height 150%, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileBodyL

// Medium body text: Decathlon Medium 16px, Line Height 150%, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileBodyM

// Small body text: Decathlon Medium 14px, Line Height 142.86%, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileBodyS
```

### Button Labels
```swift
// Medium button label: Decathlon Medium 14px, Normal Letter Spacing, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileButtonLabelM

// Small button label: Decathlon Medium 12px, Normal Letter Spacing, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileButtonLabelS
```

### Titles
```swift
// Extra large title: Decathlon Semi Bold 48px, Line Height 108.33%, Optical Sizing 30
DesignTokens.SemanticWonder.Typography.mobileTitleXl

// Large title: Decathlon Semi Bold 36px, Line Height 111.11%, Optical Sizing 30
DesignTokens.SemanticWonder.Typography.mobileTitleL

// Medium title: Decathlon Semi Bold 26px, Line Height 123.08%, Optical Sizing 30
DesignTokens.SemanticWonder.Typography.mobileTitleM

// Small title: Decathlon Semi Bold 22px, Line Height 118.18%, Optical Sizing 30
DesignTokens.SemanticWonder.Typography.mobileTitleS
```

### Subtitles
```swift
// Large subtitle: Decathlon Semi Bold 20px, Line Height 150%, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileSubtitleL

// Medium subtitle: Decathlon Semi Bold 16px, Line Height 150%, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileSubtitleM
```

### Special Text Styles
```swift
// Inspiring title: Decathlon Brand Semi Bold 48px, Line Height 108.33%, Auto Optical Sizing
DesignTokens.SemanticWonder.Typography.mobileInspiringTitleXl

// Caption: Decathlon Regular 12px, Line Height 133.33%, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileCaption

// Overline: Decathlon Semi Bold 14px, UPPERCASE, Line Height 116.67%, Optical Sizing 14
DesignTokens.SemanticWonder.Typography.mobileOverline
```

### Key Differences from Legacy
1. **Font Family**: Decathlon vs Roboto
2. **Button Labels**: Normal letter spacing vs increased letter spacing (2px)
3. **Overline**: Semi Bold (600) vs Medium (500) weight, 14px vs 12px size
4. **Optical Sizing**: Uses specific values (14, 30) vs auto
5. **Inspiring Title**: Uses Decathlon Brand font family

### Usage Example
```swift
VStack(alignment: .leading, spacing: CGFloat(DesignTokens.SemanticWonder.Spacing.s)) {
  Text("Wonder Product Title")
    .font(DesignTokens.SemanticWonder.Typography.mobileTitleM)
    .foregroundColor(DesignTokens.SemanticWonder.Color.contentNeutral)
  
  Text("Modern product description with Decathlon font...")
    .font(DesignTokens.SemanticWonder.Typography.mobileBodyM)
    .foregroundColor(DesignTokens.SemanticWonder.Color.contentQuiet)
}
```

---

## 💡 Key Differences from Legacy

1. **Sharp Design**: `main` border radius is 0px (sharp corners)
2. **Decathlon Font**: Primary font family is Decathlon instead of Roboto
3. **Extended Sizing**: Additional larger sizing tokens for more layout flexibility
4. **Optical Sizing**: Uses specific optical sizing values (14, 30) for better typography
5. **Brand Typography**: Special Decathlon Brand font for inspiring titles
6. **Refined Motion**: Slightly different easing curves for more modern feel

## 🔗 Next Steps

- [Foundation Core Cheatsheet](VitaminPlay-Foundation-Core-Cheatsheet.md)
- [Legacy Semantics Cheatsheet](VitaminPlay-Legacy-Semantics-Cheatsheet.md)
- [Component Usage Guide](VitaminPlay-Components-Cheatsheet.md)
