import Foundation
import SwiftUI

/*
 ⚠️
 Do not modify this code as it has been generated by the `swift-design-tokens-generator`.
 Any changes will be overwritten with the next generation.
 */

extension Image.Flag {
  /// ## Usage
  ///
  /// Example:
  /// ```swift
  /// Image.Flag.pl
  ///   .resizable()
  ///   .aspectRatio(contentMode: .fit)
  ///   .frame(width: 200, height: 200, alignment: .center)
  /// ```
  ///
  /// - Returns: An image representing **pl**.
  public static let pl: Image = Image("flag-pl", bundle: .assets)
}


