import Foundation
import SwiftUI

/*
 ⚠️
 Do not modify this code as it has been generated by the `swift-design-tokens-generator`.
 Any changes will be overwritten with the next generation.
 */

extension Image.Flag {
  /// ## Usage
  ///
  /// Example:
  /// ```swift
  /// Image.Flag.vc
  ///   .resizable()
  ///   .aspectRatio(contentMode: .fit)
  ///   .frame(width: 200, height: 200, alignment: .center)
  /// ```
  ///
  /// - Returns: An image representing **vc**.
  public static let vc: Image = Image("flag-vc", bundle: .assets)
}


