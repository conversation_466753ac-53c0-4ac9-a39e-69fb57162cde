import Foundation
import SwiftUI

/*
 ⚠️
 Do not modify this code as it has been generated by the `swift-design-tokens-generator`.
 Any changes will be overwritten with the next generation.
 */

extension Image.Flag {
  /// ## Usage
  ///
  /// Example:
  /// ```swift
  /// Image.Flag.ne
  ///   .resizable()
  ///   .aspectRatio(contentMode: .fit)
  ///   .frame(width: 200, height: 200, alignment: .center)
  /// ```
  ///
  /// - Returns: An image representing **ne**.
  public static let ne: Image = Image("flag-ne", bundle: .assets)
}


