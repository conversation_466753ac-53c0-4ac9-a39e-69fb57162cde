import Foundation
import SwiftUI

/*
 ⚠️
 Do not modify this code as it has been generated by the `swift-design-tokens-generator`.
 Any changes will be overwritten with the next generation.
 */

extension Image.Delivery {
  /// ## Usage
  ///
  /// Example:
  /// ```swift
  /// Image.Delivery.hermes
  ///   .resizable()
  ///   .aspectRatio(contentMode: .fit)
  ///   .frame(width: 200, height: 200, alignment: .center)
  /// ```
  ///
  /// - Returns: An image representing **hermes**.
  public static let hermes: Image = Image("delivery-hermes", bundle: .assets)
}


