import Foundation
import SwiftUI

@available(*, deprecated, renamed: "assets")
extension Bundle {
  public static let vitaminPlayBundle = Bundle.assets
}

extension Image.Flag {
#warning("The Taiwan flag has been removed -> https://github.com/dktunited/vitamin-play/blob/main/docs/ADR/Taiwan-flag-removal.md")
  @available(*, deprecated, renamed: "cn")
  public static var tw = Image.Flag.cn
  
  @available(*, deprecated, renamed: "as")
  public static var _as = Image.Flag.`as`
  
  @available(*, deprecated, renamed: "do")
  public static var _do = Image.Flag.`do`
  
  @available(*, deprecated, renamed: "in")
  public static var _in = Image.Flag.`in`
  
  @available(*, deprecated, renamed: "is")
  public static var _is = Image.Flag.`is`
}

extension Image.Icon {
  @available(*, deprecated, renamed: "repeat")
  public static var _repeat = Image.Icon.`repeat`
  
  @available(*, deprecated, renamed: "return")
  public static var _return = Image.Icon.`return`
}

extension Image.Payment {
  @available(*, deprecated, renamed: "humm")
  public static var laybuy1 = Image.Payment.humm
}

