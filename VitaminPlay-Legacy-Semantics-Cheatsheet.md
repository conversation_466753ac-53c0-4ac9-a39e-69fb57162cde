# Vitamin Play Apple SDK - Legacy Semantics Cheatsheet

## Overview
Legacy Semantics provide meaningful, context-aware design tokens that map Foundation Core values to semantic names. These tokens are specifically designed for the Legacy theme and use **Roboto** as the primary font family.

## 🔲 Border Radius

Semantic border radius values mapped from Core tokens.

### Available Values
```swift
DesignTokens.SemanticLegacy.BorderRadius.main  // Core._100 = 4px
DesignTokens.SemanticLegacy.BorderRadius.s     // Core._200 = 8px  
DesignTokens.SemanticLegacy.BorderRadius.m     // Core._400 = 16px
DesignTokens.SemanticLegacy.BorderRadius.full  // Core.full = 999px
```

### Usage Example
```swift
RoundedRectangle(cornerRadius: CGFloat(DesignTokens.SemanticLegacy.BorderRadius.m))
  .fill(Color.blue)
```

## 📏 Border Width

Semantic border width values for consistent stroke thickness.

### Available Values
```swift
DesignTokens.SemanticLegacy.BorderWidth.none  // Core._0 = 0px
DesignTokens.SemanticLegacy.BorderWidth.s     // Core._100 = 1px
DesignTokens.SemanticLegacy.BorderWidth.m     // Core._200 = 2px
DesignTokens.SemanticLegacy.BorderWidth.l     // Core._300 = 3px
```

## 🎨 Colors

Comprehensive semantic color system organized by usage context.

### Alpha Colors (Transparency)
```swift
// Brand alpha colors
DesignTokens.SemanticLegacy.Color.alphaBrandL    // Large brand transparency
DesignTokens.SemanticLegacy.Color.alphaBrandM    // Medium brand transparency
DesignTokens.SemanticLegacy.Color.alphaBrandS    // Small brand transparency

// Neutral alpha colors
DesignTokens.SemanticLegacy.Color.alphaNeutralL   // Large neutral transparency
DesignTokens.SemanticLegacy.Color.alphaNeutralM   // Medium neutral transparency
DesignTokens.SemanticLegacy.Color.alphaNeutralS   // Small neutral transparency
DesignTokens.SemanticLegacy.Color.alphaNeutralXs  // Extra small neutral transparency

// Quiet alpha colors
DesignTokens.SemanticLegacy.Color.alphaQuietM     // Medium quiet transparency
DesignTokens.SemanticLegacy.Color.alphaQuietS     // Small quiet transparency
```

### Background Colors
```swift
DesignTokens.SemanticLegacy.Color.backgroundMain         // Primary background
DesignTokens.SemanticLegacy.Color.backgroundAlternative  // Alternative background
```

### Border Colors
```swift
DesignTokens.SemanticLegacy.Color.borderBrand     // Brand border color
DesignTokens.SemanticLegacy.Color.borderCatchy    // Attention-grabbing border
DesignTokens.SemanticLegacy.Color.borderInverse   // Inverse border color
DesignTokens.SemanticLegacy.Color.borderNeutral   // Neutral border color
DesignTokens.SemanticLegacy.Color.borderOnBrand   // Border on brand backgrounds
DesignTokens.SemanticLegacy.Color.borderQuiet     // Subtle border color
```

### Container Colors
```swift
DesignTokens.SemanticLegacy.Color.containerBrand              // Brand container
DesignTokens.SemanticLegacy.Color.containerCatchy             // Catchy container
DesignTokens.SemanticLegacy.Color.containerCommercialCatchy   // Commercial catchy
DesignTokens.SemanticLegacy.Color.containerCommercialNeutral  // Commercial neutral
DesignTokens.SemanticLegacy.Color.containerNeutral            // Neutral container
DesignTokens.SemanticLegacy.Color.containerOnBrand            // Container on brand
DesignTokens.SemanticLegacy.Color.containerOnOverlay          // Container on overlay
DesignTokens.SemanticLegacy.Color.containerOverlay            // Overlay container
DesignTokens.SemanticLegacy.Color.containerQuiet              // Quiet container
```

### Content Colors
```swift
DesignTokens.SemanticLegacy.Color.contentBrand                // Brand content color
DesignTokens.SemanticLegacy.Color.contentInverse              // Inverse content
DesignTokens.SemanticLegacy.Color.contentLogo                 // Logo content
DesignTokens.SemanticLegacy.Color.contentNeutral              // Neutral content
DesignTokens.SemanticLegacy.Color.contentOnBrandAccent        // Accent on brand
DesignTokens.SemanticLegacy.Color.contentOnBrandNeutral       // Neutral on brand
DesignTokens.SemanticLegacy.Color.contentOnBrandQuiet         // Quiet on brand
DesignTokens.SemanticLegacy.Color.contentOnCommercialCatchy   // Content on commercial catchy
DesignTokens.SemanticLegacy.Color.contentOnCommercialNeutral  // Content on commercial neutral
DesignTokens.SemanticLegacy.Color.contentOnMediaAccent        // Accent on media
DesignTokens.SemanticLegacy.Color.contentOnMediaNeutral       // Neutral on media
DesignTokens.SemanticLegacy.Color.contentQuiet                // Quiet content
DesignTokens.SemanticLegacy.Color.contentService              // Service content
```

### Status Colors
```swift
DesignTokens.SemanticLegacy.Color.statusInfo       // Information status
DesignTokens.SemanticLegacy.Color.statusNegative   // Error/negative status
DesignTokens.SemanticLegacy.Color.statusPositive   // Success/positive status
DesignTokens.SemanticLegacy.Color.statusWarning    // Warning status
```

### Usage Example
```swift
VStack {
  Text("Brand Content")
    .foregroundColor(DesignTokens.SemanticLegacy.Color.contentBrand)
  Text("Quiet Content")
    .foregroundColor(DesignTokens.SemanticLegacy.Color.contentQuiet)
}
.padding()
.background(DesignTokens.SemanticLegacy.Color.containerNeutral)
```

## 🎬 Motion

Animation and transition timing values.

### Delays
```swift
DesignTokens.SemanticLegacy.Motion.delayInstant   // 0.0s
DesignTokens.SemanticLegacy.Motion.delayQuick     // 0.07s
DesignTokens.SemanticLegacy.Motion.delayModerate  // 0.17s
DesignTokens.SemanticLegacy.Motion.delaySlow      // 0.33s
DesignTokens.SemanticLegacy.Motion.delayExtended  // 0.6s
```

### Durations
```swift
DesignTokens.SemanticLegacy.Motion.durationInstant   // 0.0s
DesignTokens.SemanticLegacy.Motion.durationQuick     // 0.25s
DesignTokens.SemanticLegacy.Motion.durationModerate  // 0.6s
DesignTokens.SemanticLegacy.Motion.durationSlow      // 1.0s
DesignTokens.SemanticLegacy.Motion.durationExtended  // 1.3s
```

### Easing Functions
```swift
// Expressive easing (for brand moments)
DesignTokens.SemanticLegacy.Motion.easingExpressiveLinear
DesignTokens.SemanticLegacy.Motion.easingExpressiveEaseIn
DesignTokens.SemanticLegacy.Motion.easingExpressiveEaseOut
DesignTokens.SemanticLegacy.Motion.easingExpressiveEaseInOut

// Functional easing (for UI interactions)
DesignTokens.SemanticLegacy.Motion.easingFunctionalLinear
DesignTokens.SemanticLegacy.Motion.easingFunctionalEaseIn
DesignTokens.SemanticLegacy.Motion.easingFunctionalEaseOut
DesignTokens.SemanticLegacy.Motion.easingFunctionalEaseInOut
```

## 🔍 Opacity

Semantic opacity values for consistent transparency.

### Available Values
```swift
DesignTokens.SemanticLegacy.Opacity.none  // Core._0 = 0%
DesignTokens.SemanticLegacy.Opacity.xs    // Core._250 = 25%
DesignTokens.SemanticLegacy.Opacity.s     // Core._300 = 38%
DesignTokens.SemanticLegacy.Opacity.m     // Core._500 = 50%
DesignTokens.SemanticLegacy.Opacity.l     // Core._750 = 75%
DesignTokens.SemanticLegacy.Opacity.full  // Core._1000 = 100%
```

## 📐 Sizing

Semantic sizing values with meaningful names.

### Available Sizes
```swift
DesignTokens.SemanticLegacy.Sizing.accessibilityTouchSize  // Core._475 = 44px
DesignTokens.SemanticLegacy.Sizing.xxxs                    // Core._050 = 1px
DesignTokens.SemanticLegacy.Sizing.xxs                     // Core._150 = 4px
DesignTokens.SemanticLegacy.Sizing.xs                      // Core._200 = 8px
DesignTokens.SemanticLegacy.Sizing.s                       // Core._250 = 12px
DesignTokens.SemanticLegacy.Sizing.m                       // Core._300 = 16px
DesignTokens.SemanticLegacy.Sizing.l                       // Core._325 = 20px
DesignTokens.SemanticLegacy.Sizing.xl                      // Core._350 = 24px
DesignTokens.SemanticLegacy.Sizing.xxl                     // Core._400 = 32px
DesignTokens.SemanticLegacy.Sizing.xxxl                    // Core._450 = 40px
DesignTokens.SemanticLegacy.Sizing.xxxxl                   // Core._500 = 48px
DesignTokens.SemanticLegacy.Sizing.xxxxxl                  // Core._520 = 56px
DesignTokens.SemanticLegacy.Sizing.xxxxxxl                 // Core._550 = 64px
```

## 📏 Spacing

Semantic spacing values for consistent layouts.

### Available Spacing
```swift
DesignTokens.SemanticLegacy.Spacing.xxxxs      // Core._0 = 0px
DesignTokens.SemanticLegacy.Spacing.xxxs       // Core._025 = 2px
DesignTokens.SemanticLegacy.Spacing.xxs        // Core._050 = 4px
DesignTokens.SemanticLegacy.Spacing.xs         // Core._100 = 8px
DesignTokens.SemanticLegacy.Spacing.s          // Core._150 = 12px
DesignTokens.SemanticLegacy.Spacing.m          // Core._200 = 16px
DesignTokens.SemanticLegacy.Spacing.l          // Core._250 = 20px
DesignTokens.SemanticLegacy.Spacing.xl         // Core._300 = 24px
DesignTokens.SemanticLegacy.Spacing.xxl        // Core._400 = 32px
DesignTokens.SemanticLegacy.Spacing.xxxl       // Core._500 = 40px
DesignTokens.SemanticLegacy.Spacing.xxxxl      // Core._600 = 48px
DesignTokens.SemanticLegacy.Spacing.xxxxxl     // Core._650 = 64px
DesignTokens.SemanticLegacy.Spacing.xxxxxxl    // Core._700 = 80px
DesignTokens.SemanticLegacy.Spacing.xxxxxxxl   // Core._800 = 96px
DesignTokens.SemanticLegacy.Spacing.xxxxxxxxl  // Core._850 = 128px
DesignTokens.SemanticLegacy.Spacing.xxxxxxxxxl // Core._900 = 160px
```

### Usage Example
```swift
VStack(spacing: CGFloat(DesignTokens.SemanticLegacy.Spacing.m)) {
  Text("Title")
  Text("Description")
}
.padding(CGFloat(DesignTokens.SemanticLegacy.Spacing.xl))
```

## 🔤 Typography

Legacy typography system using **Roboto** font family.

### Body Text
```swift
// Large body text: Roboto Medium 20px, Line Height 150%
DesignTokens.SemanticLegacy.Typography.mobileBodyL

// Medium body text: Roboto Medium 16px, Line Height 150%  
DesignTokens.SemanticLegacy.Typography.mobileBodyM

// Small body text: Roboto Medium 14px, Line Height 142.86%
DesignTokens.SemanticLegacy.Typography.mobileBodyS
```

### Button Labels
```swift
// Medium button label: Roboto Medium 14px, Letter Spacing 2px
DesignTokens.SemanticLegacy.Typography.mobileButtonLabelM

// Small button label: Roboto Medium 12px, Letter Spacing 2px
DesignTokens.SemanticLegacy.Typography.mobileButtonLabelS
```

### Titles
```swift
// Extra large title: Roboto Semi Bold 48px, Line Height 108.33%
DesignTokens.SemanticLegacy.Typography.mobileTitleXl

// Large title: Roboto Semi Bold 36px, Line Height 111.11%
DesignTokens.SemanticLegacy.Typography.mobileTitleL

// Medium title: Roboto Semi Bold 26px, Line Height 123.08%
DesignTokens.SemanticLegacy.Typography.mobileTitleM

// Small title: Roboto Semi Bold 22px, Line Height 118.18%
DesignTokens.SemanticLegacy.Typography.mobileTitleS
```

### Subtitles
```swift
// Large subtitle: Roboto Semi Bold 20px, Line Height 150%
DesignTokens.SemanticLegacy.Typography.mobileSubtitleL

// Medium subtitle: Roboto Semi Bold 16px, Line Height 150%
DesignTokens.SemanticLegacy.Typography.mobileSubtitleM
```

### Special Text Styles
```swift
// Inspiring title: Roboto Semi Bold 48px, Line Height 108.33%
DesignTokens.SemanticLegacy.Typography.mobileInspiringTitleXl

// Caption: Roboto Regular 12px, Line Height 133.33%
DesignTokens.SemanticLegacy.Typography.mobileCaption

// Overline: Roboto Medium 12px, UPPERCASE, Line Height 116.67%
DesignTokens.SemanticLegacy.Typography.mobileOverline
```

### Usage Example
```swift
VStack(alignment: .leading, spacing: CGFloat(DesignTokens.SemanticLegacy.Spacing.s)) {
  Text("Product Title")
    .font(DesignTokens.SemanticLegacy.Typography.mobileTitleM)
    .foregroundColor(DesignTokens.SemanticLegacy.Color.contentNeutral)
  
  Text("Product description goes here...")
    .font(DesignTokens.SemanticLegacy.Typography.mobileBodyM)
    .foregroundColor(DesignTokens.SemanticLegacy.Color.contentQuiet)
}
```

---

## 💡 Key Differences from Core

1. **Semantic Names**: Instead of numeric values, uses meaningful names like `main`, `s`, `m`, `l`
2. **Context-Aware**: Colors are organized by usage context (content, container, border, etc.)
3. **Roboto Font**: All typography uses Roboto as the primary font family
4. **Motion System**: Includes comprehensive animation timing and easing functions
5. **Accessibility**: Includes `accessibilityTouchSize` for minimum touch targets

## 🔗 Next Steps

- [Foundation Core Cheatsheet](VitaminPlay-Foundation-Core-Cheatsheet.md)
- [Wonder Semantics Cheatsheet](VitaminPlay-Wonder-Semantics-Cheatsheet.md)
- [Component Usage Guide](VitaminPlay-Components-Cheatsheet.md)
