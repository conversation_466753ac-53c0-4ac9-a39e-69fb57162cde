# Button Component - Vitamin Play Apple SDK

## 📋 Overview

The **Button** component is a fundamental UI element that communicates what actions users can perform. Use buttons to let users perform actions and make choices with a click or tap. The Button component provides consistent visual styling across Legacy and Wonder themes with extensive customization options.

**Component Type:** `VitaminPlayButtonStyle` (ButtonStyle)  
**Platform Support:** iOS 13.0+, macOS 10.15+, tvOS 13.0+, watchOS 6.0+

---

## 🎯 Key Features

- **Theme Support**: Full Legacy and Wonder theme integration
- **Multiple Variants**: Primary, Secondary, Tertiary, Negative styles
- **Size Options**: Small, Medium, Large sizing
- **Corner Styles**: Rounded and Squared corner options
- **Mode Support**: Regular and Inverse display modes
- **Icon Integration**: Leading and trailing icon support
- **State Management**: Normal, Pressed, Disabled, Hover states
- **Accessibility**: Built-in accessibility traits and support
- **Animation**: Smooth state transitions

---

## 🔧 Basic Usage

### Simple Button
````swift
Button("Click Me") {
    // Action code here
}
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumPrimary))
````

### Button with Icon
````swift
Button(action: { }) {
    Label("Vitamin Play".uppercased(), systemImage: "chevron.forward")
}
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderRoundedRegularLargePrimary))
````

### Button with Custom Start Icon
````swift
Button("Save") {
    // Save action
}
.buttonStyle(VitaminPlayButtonStyle(
    theme: .legacyRoundedRegularMediumSecondary,
    { Image(systemName: "checkmark") }
))
````

---

## ⚙️ Configuration Options

### Theme Structure
The button theme follows this pattern:
**`[Theme][Corner][Mode][Size][Variant]`**

### Available Options:

#### **Themes**
- **Legacy**: Traditional Roboto-based styling
- **Wonder**: Modern Decathlon-based styling

#### **Corner Styles**
- **Rounded**: Curved corners (default)
- **Squared**: Sharp corners

#### **Modes**
- **Regular**: Standard appearance
- **Inverse**: Inverted color scheme

#### **Sizes**
- **Small**: Compact button size
- **Medium**: Standard button size (recommended)
- **Large**: Prominent button size

#### **Variants**
- **Primary**: Main action button (filled)
- **Secondary**: Secondary action (outlined)
- **Tertiary**: Subtle action (text-only)
- **Negative**: Destructive action (red/warning)

---

## 🎨 Theme Examples

### Legacy Theme Buttons
````swift
// Primary button - most important action
Button("Continue") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumPrimary))

// Secondary button - secondary action
Button("Cancel") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumSecondary))

// Tertiary button - subtle action
Button("Learn More") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumTertiary))

// Negative button - destructive action
Button("Delete") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumNegative))
````

### Wonder Theme Buttons
````swift
// Wonder primary with large size
Button("Get Started") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderRoundedRegularLargePrimary))

// Wonder squared corners
Button("Settings") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderSquaredRegularMediumSecondary))

// Wonder inverse mode
Button("Highlight") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderRoundedInverseMediumPrimary))
````

---

## 🔄 Advanced Usage

### Programmatic Theme Configuration
````swift
Button("Dynamic Button") { }
.buttonStyle(VitaminPlayButtonStyle(
    generalTheme: .wonder,
    corner: .rounded,
    mode: .regular,
    size: .large,
    variant: .primary
))
````

### Button with Multiple Icons
````swift
Button(action: { }) {
    Label("Download".uppercased(), systemImage: "arrow.down")
}
.buttonStyle(VitaminPlayButtonStyle(
    theme: .legacyRoundedRegularMediumPrimary,
    { Image(systemName: "icloud") },
    labelIconPosition: .leading
))
````

### Disabled State
````swift
Button("Unavailable") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumPrimary))
.disabled(true)
````

---

## 📐 Detailed Specifications

### Size Specifications

#### **Small Buttons**
- **Padding**: 8px vertical, 16px horizontal
- **Gap**: 2px (between icon and text)
- **Typography**: 12px Decathlon/Roboto Medium
- **Icon Scale**: Small (.small)

#### **Medium Buttons**
- **Padding**: 12px vertical, 20px horizontal
- **Gap**: 4px (between icon and text)
- **Typography**: 14px Decathlon/Roboto Medium
- **Icon Scale**: Medium (.medium)

#### **Large Buttons**
- **Padding**: 20px vertical, 32px horizontal
- **Gap**: 8px (between icon and text)
- **Typography**: 14px Decathlon/Roboto Medium
- **Icon Scale**: Large (.large)

### Corner Radius Specifications

#### **Rounded Corners**
- **Legacy**: 999px (full capsule)
- **Wonder**: 999px (full capsule)

#### **Squared Corners**
- **Legacy**: 8px radius
- **Wonder**: 0px radius (sharp corners)

### Border Width Specifications
- **All Variants**: 1px border width
- **Transparent borders** for primary buttons
- **Visible borders** for secondary/tertiary variants

### Typography Specifications

#### **Legacy Theme (Roboto)**
- **Button Label M**: Roboto Medium 14px, Line Height 170%, Letter Spacing +2px
- **Button Label S**: Roboto Medium 12px, Line Height 130%, Letter Spacing +2px

#### **Wonder Theme (Decathlon)**
- **Button Label M**: Decathlon Medium 14px, Line Height 170%, Optical Sizing 14
- **Button Label S**: Decathlon Medium 12px, Line Height 130%, Optical Sizing 14

### Accessibility Specifications
- **Minimum Touch Target**: 44px × 44px (iOS accessibility standard)
- **Disabled Opacity**: 38% opacity when disabled
- **Focus States**: Built-in focus ring support
- **VoiceOver**: Automatic accessibility traits

### State Color Examples (Wonder Primary)
- **Enabled**: Brand container color
- **Pressed**: Darker pressed state color
- **Disabled**: 38% opacity of enabled state
- **Text Color**: White on brand background

---

## 📐 Component Structure

### ButtonTheme Properties
- **cornerRadius**: VitaminCornerRadii (0px, 8px, 20px, or 999px)
- **borderWidth**: CGFloat (typically 1px)
- **backgroundColor**: ButtonColor (state-based colors)
- **borderColor**: ButtonColor (state-based border colors)
- **foregroundColor**: ButtonColor (text/icon colors)
- **disableOpacity**: UInt (38% for disabled state)
- **typography**: TextConfiguration (font specifications)
- **padding**: EdgeInsets (size-specific padding)
- **gap**: CGFloat (icon-to-text spacing)

### ButtonState Enum
- **pressed**: Button is being pressed
- **enabled**: Button is interactive (default)
- **disabled**: Button is non-interactive

### IconPosition Enum
- **leading**: Icon appears before text
- **trailing**: Icon appears after text (default)

---

## 🎯 Best Practices

### ✅ Do
- Use Primary buttons for the most important action
- Use consistent sizing within the same interface
- Provide clear, action-oriented button labels
- Use icons to enhance understanding, not replace text
- Test button accessibility with VoiceOver

### ❌ Don't
- Use more than one Primary button per screen section
- Make buttons too small (respect minimum touch targets)
- Use vague labels like "OK" or "Submit"
- Override theme colors unless absolutely necessary
- Stack too many buttons vertically

---

## 🔗 Related Components

- **IconButton**: Icon-only button variant
- **Link**: Text-based link button
- **Chip**: Compact action buttons
- **Toggle**: Binary state buttons

---

## 📱 Platform Notes

- **iOS/macOS**: Full feature support including hover states
- **tvOS**: Focus-based interaction support
- **watchOS**: Optimized for smaller screens
- **visionOS**: Spatial interaction support

---

*This documentation covers the Button component. For complete theme specifications, see the Foundation Core and Semantic Design Token documentation.*
