# Button Component - Vitamin Play Apple SDK

## 📋 Overview

The **Button** component is a fundamental UI element that communicates what actions users can perform. Use buttons to let users perform actions and make choices with a click or tap. The Button component provides consistent visual styling across Legacy and Wonder themes with extensive customization options.

**Component Type:** `VitaminPlayButtonStyle` (ButtonStyle)  
**Platform Support:** iOS 13.0+, macOS 10.15+, tvOS 13.0+, watchOS 6.0+

---

## 🎯 Key Features

- **Theme Support**: Full Legacy and Wonder theme integration
- **Multiple Variants**: Primary, Secondary, Tertiary, Negative styles
- **Size Options**: Small, Medium, Large sizing
- **Corner Styles**: Rounded and Squared corner options
- **Mode Support**: Regular and Inverse display modes
- **Icon Integration**: Leading and trailing icon support
- **State Management**: Normal, Pressed, Disabled, Hover states
- **Accessibility**: Built-in accessibility traits and support
- **Animation**: Smooth state transitions

---

## 🔧 Basic Usage

### Simple Button
````swift
Button("Click Me") {
    // Action code here
}
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumPrimary))
````

### Button with Icon
````swift
Button(action: { }) {
    Label("Vitamin Play".uppercased(), systemImage: "chevron.forward")
}
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderRoundedRegularLargePrimary))
````

### Button with Custom Start Icon
````swift
Button("Save") {
    // Save action
}
.buttonStyle(VitaminPlayButtonStyle(
    theme: .legacyRoundedRegularMediumSecondary,
    { Image(systemName: "checkmark") }
))
````

---

## ⚙️ Configuration Options

### Theme Structure
The button theme follows this pattern:
**`[Theme][Corner][Mode][Size][Variant]`**

### Available Options:

#### **Themes**
- **Legacy**: Traditional Roboto-based styling
- **Wonder**: Modern Decathlon-based styling

#### **Corner Styles**
- **Rounded**: Curved corners (default)
- **Squared**: Sharp corners

#### **Modes**
- **Regular**: Standard appearance
- **Inverse**: Inverted color scheme

#### **Sizes**
- **Small**: 8px vertical, 16px horizontal padding • 12px font • 2px icon gap
- **Medium**: 12px vertical, 20px horizontal padding • 14px font • 4px icon gap (recommended)
- **Large**: 20px vertical, 32px horizontal padding • 14px font • 8px icon gap

#### **Corner Styles**
- **Rounded**: 999px radius (full capsule)
- **Squared**: 8px radius (Legacy) / 0px radius (Wonder - sharp corners)

#### **Variants**
- **Primary**: Main action button (filled)
- **Secondary**: Secondary action (outlined)
- **Tertiary**: Subtle action (text-only)
- **Negative**: Destructive action (red/warning)

---

## 🎨 Theme Examples

### Legacy Theme Buttons
````swift
// Primary button - most important action
Button("Continue") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumPrimary))

// Secondary button - secondary action
Button("Cancel") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumSecondary))

// Tertiary button - subtle action
Button("Learn More") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumTertiary))

// Negative button - destructive action
Button("Delete") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumNegative))
````

### Wonder Theme Buttons
````swift
// Wonder primary with large size
Button("Get Started") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderRoundedRegularLargePrimary))

// Wonder squared corners
Button("Settings") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderSquaredRegularMediumSecondary))

// Wonder inverse mode
Button("Highlight") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderRoundedInverseMediumPrimary))
````

---

## 🔄 Advanced Usage

### Programmatic Theme Configuration
````swift
Button("Dynamic Button") { }
.buttonStyle(VitaminPlayButtonStyle(
    generalTheme: .wonder,
    corner: .rounded,
    mode: .regular,
    size: .large,
    variant: .primary
))
````

### Button with Multiple Icons
````swift
Button(action: { }) {
    Label("Download".uppercased(), systemImage: "arrow.down")
}
.buttonStyle(VitaminPlayButtonStyle(
    theme: .legacyRoundedRegularMediumPrimary,
    { Image(systemName: "icloud") },
    labelIconPosition: .leading
))
````

### Disabled State
````swift
Button("Unavailable") { }
.buttonStyle(VitaminPlayButtonStyle(theme: .legacyRoundedRegularMediumPrimary))
.disabled(true)
````

---

## 📐 Component Structure

### ButtonTheme Properties
- **cornerRadius**: VitaminCornerRadii (0px, 8px, 20px, or 999px)
- **borderWidth**: CGFloat (typically 1px)
- **backgroundColor**: ButtonColor (state-based colors)
- **borderColor**: ButtonColor (state-based border colors)
- **foregroundColor**: ButtonColor (text/icon colors)
- **disableOpacity**: UInt (38% for disabled state)
- **typography**: TextConfiguration (font specifications)
- **padding**: EdgeInsets (size-specific padding)
- **gap**: CGFloat (icon-to-text spacing)

### ButtonState Enum
- **pressed**: Button is being pressed
- **enabled**: Button is interactive (default)
- **disabled**: Button is non-interactive

### IconPosition Enum
- **leading**: Icon appears before text
- **trailing**: Icon appears after text (default)

---

*This documentation covers the Button component. For complete theme specifications, see the Foundation Core and Semantic Design Token documentation.*
