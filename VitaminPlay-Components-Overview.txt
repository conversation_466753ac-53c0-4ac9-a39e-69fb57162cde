# Components - Vitamin Play Apple SDK

## 📋 Overview

The **Vitamin Play Apple SDK** provides a comprehensive collection of **27 pre-built UI components** designed for consistent, accessible, and beautiful user interfaces across iOS, macOS, tvOS, and watchOS platforms.

All components are built with **SwiftUI** and integrate seamlessly with the **Foundation Core Design Tokens** and **Semantic Design Systems** (Legacy and Wonder themes).

**Platform Support:** iOS 13.0+, macOS 10.15+, tvOS 13.0+, watchOS 6.0+

---

## 🎯 Component Categories

### **Form Controls (7 components)**
Essential input and selection components for user interaction.

- **[Button](VitaminPlay-Button-Component-Documentation.txt)** - Primary action buttons with multiple variants and themes
- **TextField** - Text input fields with validation and styling
- **TextEditor** - Multi-line text editing component
- **Checkbox** - Binary selection with custom styling
- **Toggle** - Switch-style binary controls
- **Select** - Dropdown selection component
- **SearchInput** - Specialized search input field

### **Interactive Elements (4 components)**
Specialized interactive components for enhanced user experience.

- **IconButton** - Icon-only button variant for compact interfaces
- **IconToggle** - Toggle button with icon states
- **Link** - Text-based navigation links
- **TabView** - Tab-based navigation component

### **Content Display (6 components)**
Components for displaying and organizing content.

- **Badge** - Status indicators and labels
- **Sticker** - Decorative content labels
- **Loader** - Loading state indicators
- **Divider** - Visual content separators
- **Price** - Formatted price display component
- **Accordion** - Collapsible content sections

### **Selection & Filtering (4 components)**
Chip-based components for selection and filtering interfaces.

- **ActionChip** - Actionable chip buttons
- **FilterChip** - Filterable selection chips
- **InputChip** - Input-based chip component
- **FilterCombinedChip** - Combined filter chip variant

### **Rating & Feedback (2 components)**
Components for user ratings and feedback collection.

- **StarRating** - Star-based rating component
- **ScoreRating** - Numeric score rating component

### **Card Components (3 components)**
Structured content containers for different use cases.

- **ProductCard** - E-commerce product display cards
- **ArticleCard** - Article and content preview cards
- **FeatureCard** - Feature highlight cards

### **System Components (1 component)**
System-level appearance and theming components.

- **Appearance** - Theme and appearance management

---

## 🔧 Common Features

All components share these consistent features:

### **🎨 Dual Theme Support**
- **Legacy Theme**: Traditional Roboto-based styling
- **Wonder Theme**: Modern Decathlon-based styling

### **📱 Multi-Platform**
- **iOS**: Full feature support with native interactions
- **macOS**: Desktop-optimized layouts and hover states
- **tvOS**: Focus-based navigation support
- **watchOS**: Compact, wrist-optimized interfaces

### **♿ Accessibility Built-In**
- **VoiceOver**: Automatic accessibility traits
- **Touch Targets**: 44px minimum touch targets
- **Focus Management**: Keyboard and remote navigation
- **High Contrast**: Support for accessibility display modes

### **🎯 Consistent API**
- **SwiftUI Native**: Built with SwiftUI best practices
- **Environment Integration**: Automatic theme detection
- **Modifier Support**: Standard SwiftUI modifiers work seamlessly
- **State Management**: Reactive state handling

---

## 🚀 Getting Started

### Basic Component Usage
````swift
import VitaminPlay

// Simple button with Wonder theme
Button("Get Started") {
    // Action code
}
.buttonStyle(VitaminPlayButtonStyle(theme: .wonderRoundedRegularMediumPrimary))

// Text field with validation
TextField("Enter your name", text: $name)
.textFieldStyle(VitaminPlayTextFieldStyle(theme: .legacyRegularMediumDefault))
````

### Theme Configuration
````swift
// Set global theme preference
@Environment(\.vitaminTheme) var theme

// Use programmatic theme selection
Button("Dynamic Button") { }
.buttonStyle(VitaminPlayButtonStyle(
    generalTheme: .wonder,
    corner: .rounded,
    mode: .regular,
    size: .large,
    variant: .primary
))
````

---

## 📚 Documentation Structure

Each component documentation includes:

- **📋 Overview**: Component purpose and platform support
- **🎯 Key Features**: Main capabilities and variants
- **🔧 Basic Usage**: Simple implementation examples
- **⚙️ Configuration**: All available options with specifications
- **🎨 Theme Examples**: Legacy and Wonder theme usage
- **🔄 Advanced Usage**: Complex scenarios and customization
- **📐 Component Structure**: Technical implementation details

---

## 🔗 Related Documentation

- **[Foundation Core Design Tokens](VitaminPlay-Foundation-Core-Complete-Reference.txt)** - Core design system values
- **[Legacy Semantics](VitaminPlay-Legacy-Semantics-Cheatsheet.md)** - Traditional theme specifications
- **[Wonder Semantics](VitaminPlay-Wonder-Semantics-Cheatsheet.md)** - Modern theme specifications
- **[Design System Overview](VitaminPlay-Design-System-Overview.txt)** - Complete system introduction

---

*This overview covers all 27 components in the Vitamin Play Apple SDK. Click on any component name to view its detailed documentation.*
