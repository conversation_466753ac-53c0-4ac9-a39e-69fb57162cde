# Vitamin Play Apple SDK - Foundation Core Design Tokens

## Complete Reference with Code Snippets

This document provides a comprehensive reference of all Foundation Core design tokens available in the Vitamin Play Apple SDK, with exact code snippets and values.

---

## 1. Aspect Ratio (16 ratios)

### Square Ratios
```swift
DesignTokens.Core.AspectRatio.oneByOne        // 1.0/1.0 = 1.0
```

### Portrait Ratios
```swift
DesignTokens.Core.AspectRatio.fourByFive      // 4.0/5.0 = 0.8
DesignTokens.Core.AspectRatio.threeByFour     // 3.0/4.0 = 0.75
DesignTokens.Core.AspectRatio.nineBySixteen   // 9.0/16.0 = 0.5625
DesignTokens.Core.AspectRatio.oneByTwo        // 1.0/2.0 = 0.5
DesignTokens.Core.AspectRatio.twoByThree      // 2.0/3.0 = 0.667
DesignTokens.Core.AspectRatio.tenBySixteen    // 10.0/16.0 = 0.625
DesignTokens.Core.AspectRatio.nineByTwentyOne // 9.0/21.0 = 0.429
```

### Landscape Ratios
```swift
DesignTokens.Core.AspectRatio.fiveByFour      // 5.0/4.0 = 1.25
DesignTokens.Core.AspectRatio.fourByThree     // 4.0/3.0 = 1.333
DesignTokens.Core.AspectRatio.threeByTwo      // 3.0/2.0 = 1.5
DesignTokens.Core.AspectRatio.golden          // 1.618/1.0 = 1.618
DesignTokens.Core.AspectRatio.sixteenByTen    // 16.0/10.0 = 1.6
DesignTokens.Core.AspectRatio.sixteenByNine   // 16.0/9.0 = 1.778
DesignTokens.Core.AspectRatio.twoByOne        // 2.0/1.0 = 2.0
DesignTokens.Core.AspectRatio.twentyOneByNine // 21.0/9.0 = 2.333
```

### Usage Example
```swift
Image("product-image")
  .resizable()
  .aspectRatio(DesignTokens.Core.AspectRatio.oneByOne, contentMode: .fit)
  .frame(width: 200, height: 200)
```

---

## 2. Border Radius (9 values)

```swift
DesignTokens.Core.BorderRadius._0     // 0px (sharp corners)
DesignTokens.Core.BorderRadius._050   // 2px
DesignTokens.Core.BorderRadius._100   // 4px
DesignTokens.Core.BorderRadius._200   // 8px
DesignTokens.Core.BorderRadius._300   // 12px
DesignTokens.Core.BorderRadius._400   // 16px
DesignTokens.Core.BorderRadius._500   // 20px
DesignTokens.Core.BorderRadius._600   // 28px
DesignTokens.Core.BorderRadius.full   // 999px (fully rounded)
```

### Usage Example
```swift
RoundedRectangle(cornerRadius: CGFloat(DesignTokens.Core.BorderRadius._200))
  .fill(Color.blue)
  .frame(width: 100, height: 100)
```

---

## 3. Border Width (4 values)

```swift
DesignTokens.Core.BorderWidth._0     // 0px (no border)
DesignTokens.Core.BorderWidth._100   // 1px
DesignTokens.Core.BorderWidth._200   // 2px
DesignTokens.Core.BorderWidth._300   // 3px
```

### Usage Example
```swift
Rectangle()
  .stroke(Color.gray, lineWidth: CGFloat(DesignTokens.Core.BorderWidth._100))
```

---

## 4. Colors (113 colors across 12 families)

### Base Colors
```swift
DesignTokens.Core.Color.black         // SwiftUI.Color("coreBlack", bundle: .colors)
DesignTokens.Core.Color.white         // SwiftUI.Color("coreWhite", bundle: .colors)
DesignTokens.Core.Color.transparent   // SwiftUI.Color.clear
```

### Blue Family (9 colors)
```swift
DesignTokens.Core.Color.blue050       // SwiftUI.Color("coreBlue050", bundle: .colors)
DesignTokens.Core.Color.blue100       // SwiftUI.Color("coreBlue100", bundle: .colors)
DesignTokens.Core.Color.blue200       // SwiftUI.Color("coreBlue200", bundle: .colors)
DesignTokens.Core.Color.blue300       // SwiftUI.Color("coreBlue300", bundle: .colors)
DesignTokens.Core.Color.blue400       // SwiftUI.Color("coreBlue400", bundle: .colors)
DesignTokens.Core.Color.blue450       // SwiftUI.Color("coreBlue450", bundle: .colors)
DesignTokens.Core.Color.blue500       // SwiftUI.Color("coreBlue500", bundle: .colors)
DesignTokens.Core.Color.blue600       // SwiftUI.Color("coreBlue600", bundle: .colors)
DesignTokens.Core.Color.blue700       // SwiftUI.Color("coreBlue700", bundle: .colors)
```

### Cobalt Family (10 colors)
```swift
DesignTokens.Core.Color.cobalt010     // SwiftUI.Color("coreCobalt010", bundle: .colors)
DesignTokens.Core.Color.cobalt050     // SwiftUI.Color("coreCobalt050", bundle: .colors)
DesignTokens.Core.Color.cobalt100     // SwiftUI.Color("coreCobalt100", bundle: .colors)
DesignTokens.Core.Color.cobalt200     // SwiftUI.Color("coreCobalt200", bundle: .colors)
DesignTokens.Core.Color.cobalt300     // SwiftUI.Color("coreCobalt300", bundle: .colors)
DesignTokens.Core.Color.cobalt400     // SwiftUI.Color("coreCobalt400", bundle: .colors)
DesignTokens.Core.Color.cobalt500     // SwiftUI.Color("coreCobalt500", bundle: .colors)
DesignTokens.Core.Color.cobalt600     // SwiftUI.Color("coreCobalt600", bundle: .colors)
DesignTokens.Core.Color.cobalt700     // SwiftUI.Color("coreCobalt700", bundle: .colors)
DesignTokens.Core.Color.cobalt800     // SwiftUI.Color("coreCobalt800", bundle: .colors)
DesignTokens.Core.Color.cobalt900     // SwiftUI.Color("coreCobalt900", bundle: .colors)
```

### Conifer Family (8 colors)
```swift
DesignTokens.Core.Color.conifer050    // SwiftUI.Color("coreConifer050", bundle: .colors)
DesignTokens.Core.Color.conifer100    // SwiftUI.Color("coreConifer100", bundle: .colors)
DesignTokens.Core.Color.conifer200    // SwiftUI.Color("coreConifer200", bundle: .colors)
DesignTokens.Core.Color.conifer300    // SwiftUI.Color("coreConifer300", bundle: .colors)
DesignTokens.Core.Color.conifer400    // SwiftUI.Color("coreConifer400", bundle: .colors)
DesignTokens.Core.Color.conifer500    // SwiftUI.Color("coreConifer500", bundle: .colors)
DesignTokens.Core.Color.conifer600    // SwiftUI.Color("coreConifer600", bundle: .colors)
DesignTokens.Core.Color.conifer700    // SwiftUI.Color("coreConifer700", bundle: .colors)
```

### Gold Family (8 colors)
```swift
DesignTokens.Core.Color.gold050       // SwiftUI.Color("coreGold050", bundle: .colors)
DesignTokens.Core.Color.gold100       // SwiftUI.Color("coreGold100", bundle: .colors)
DesignTokens.Core.Color.gold200       // SwiftUI.Color("coreGold200", bundle: .colors)
DesignTokens.Core.Color.gold300       // SwiftUI.Color("coreGold300", bundle: .colors)
DesignTokens.Core.Color.gold400       // SwiftUI.Color("coreGold400", bundle: .colors)
DesignTokens.Core.Color.gold500       // SwiftUI.Color("coreGold500", bundle: .colors)
DesignTokens.Core.Color.gold600       // SwiftUI.Color("coreGold600", bundle: .colors)
DesignTokens.Core.Color.gold700       // SwiftUI.Color("coreGold700", bundle: .colors)
```

### Green Family (8 colors)
```swift
DesignTokens.Core.Color.green050      // SwiftUI.Color("coreGreen050", bundle: .colors)
DesignTokens.Core.Color.green100      // SwiftUI.Color("coreGreen100", bundle: .colors)
DesignTokens.Core.Color.green200      // SwiftUI.Color("coreGreen200", bundle: .colors)
DesignTokens.Core.Color.green300      // SwiftUI.Color("coreGreen300", bundle: .colors)
DesignTokens.Core.Color.green400      // SwiftUI.Color("coreGreen400", bundle: .colors)
DesignTokens.Core.Color.green500      // SwiftUI.Color("coreGreen500", bundle: .colors)
DesignTokens.Core.Color.green600      // SwiftUI.Color("coreGreen600", bundle: .colors)
DesignTokens.Core.Color.green700      // SwiftUI.Color("coreGreen700", bundle: .colors)
```

### Grey Family (11 colors)
```swift
DesignTokens.Core.Color.grey050       // SwiftUI.Color("coreGrey050", bundle: .colors)
DesignTokens.Core.Color.grey100       // SwiftUI.Color("coreGrey100", bundle: .colors)
DesignTokens.Core.Color.grey200       // SwiftUI.Color("coreGrey200", bundle: .colors)
DesignTokens.Core.Color.grey300       // SwiftUI.Color("coreGrey300", bundle: .colors)
DesignTokens.Core.Color.grey400       // SwiftUI.Color("coreGrey400", bundle: .colors)
DesignTokens.Core.Color.grey500       // SwiftUI.Color("coreGrey500", bundle: .colors)
DesignTokens.Core.Color.grey600       // SwiftUI.Color("coreGrey600", bundle: .colors)
DesignTokens.Core.Color.grey700       // SwiftUI.Color("coreGrey700", bundle: .colors)
DesignTokens.Core.Color.grey800       // SwiftUI.Color("coreGrey800", bundle: .colors)
DesignTokens.Core.Color.grey900       // SwiftUI.Color("coreGrey900", bundle: .colors)
DesignTokens.Core.Color.grey950       // SwiftUI.Color("coreGrey950", bundle: .colors)
```

### Mint Family (10 colors)
```swift
DesignTokens.Core.Color.mint050       // SwiftUI.Color("coreMint050", bundle: .colors)
DesignTokens.Core.Color.mint100       // SwiftUI.Color("coreMint100", bundle: .colors)
DesignTokens.Core.Color.mint200       // SwiftUI.Color("coreMint200", bundle: .colors)
DesignTokens.Core.Color.mint300       // SwiftUI.Color("coreMint300", bundle: .colors)
DesignTokens.Core.Color.mint400       // SwiftUI.Color("coreMint400", bundle: .colors)
DesignTokens.Core.Color.mint500       // SwiftUI.Color("coreMint500", bundle: .colors)
DesignTokens.Core.Color.mint600       // SwiftUI.Color("coreMint600", bundle: .colors)
DesignTokens.Core.Color.mint700       // SwiftUI.Color("coreMint700", bundle: .colors)
DesignTokens.Core.Color.mint800       // SwiftUI.Color("coreMint800", bundle: .colors)
DesignTokens.Core.Color.mint900       // SwiftUI.Color("coreMint900", bundle: .colors)
```

### Orange Family (8 colors)
```swift
DesignTokens.Core.Color.orange050     // SwiftUI.Color("coreOrange050", bundle: .colors)
DesignTokens.Core.Color.orange100     // SwiftUI.Color("coreOrange100", bundle: .colors)
DesignTokens.Core.Color.orange200     // SwiftUI.Color("coreOrange200", bundle: .colors)
DesignTokens.Core.Color.orange300     // SwiftUI.Color("coreOrange300", bundle: .colors)
DesignTokens.Core.Color.orange400     // SwiftUI.Color("coreOrange400", bundle: .colors)
DesignTokens.Core.Color.orange500     // SwiftUI.Color("coreOrange500", bundle: .colors)
DesignTokens.Core.Color.orange600     // SwiftUI.Color("coreOrange600", bundle: .colors)
DesignTokens.Core.Color.orange700     // SwiftUI.Color("coreOrange700", bundle: .colors)
```

### Purple Family (8 colors)
```swift
DesignTokens.Core.Color.purple050     // SwiftUI.Color("corePurple050", bundle: .colors)
DesignTokens.Core.Color.purple100     // SwiftUI.Color("corePurple100", bundle: .colors)
DesignTokens.Core.Color.purple200     // SwiftUI.Color("corePurple200", bundle: .colors)
DesignTokens.Core.Color.purple300     // SwiftUI.Color("corePurple300", bundle: .colors)
DesignTokens.Core.Color.purple400     // SwiftUI.Color("corePurple400", bundle: .colors)
DesignTokens.Core.Color.purple500     // SwiftUI.Color("corePurple500", bundle: .colors)
DesignTokens.Core.Color.purple600     // SwiftUI.Color("corePurple600", bundle: .colors)
DesignTokens.Core.Color.purple700     // SwiftUI.Color("corePurple700", bundle: .colors)
```

### Red Family (8 colors)
```swift
DesignTokens.Core.Color.red050        // SwiftUI.Color("coreRed050", bundle: .colors)
DesignTokens.Core.Color.red100        // SwiftUI.Color("coreRed100", bundle: .colors)
DesignTokens.Core.Color.red200        // SwiftUI.Color("coreRed200", bundle: .colors)
DesignTokens.Core.Color.red300        // SwiftUI.Color("coreRed300", bundle: .colors)
DesignTokens.Core.Color.red400        // SwiftUI.Color("coreRed400", bundle: .colors)
DesignTokens.Core.Color.red500        // SwiftUI.Color("coreRed500", bundle: .colors)
DesignTokens.Core.Color.red600        // SwiftUI.Color("coreRed600", bundle: .colors)
DesignTokens.Core.Color.red700        // SwiftUI.Color("coreRed700", bundle: .colors)
```

### Rock Family (12 colors)
```swift
DesignTokens.Core.Color.rock010       // SwiftUI.Color("coreRock010", bundle: .colors)
DesignTokens.Core.Color.rock020       // SwiftUI.Color("coreRock020", bundle: .colors)
DesignTokens.Core.Color.rock050       // SwiftUI.Color("coreRock050", bundle: .colors)
DesignTokens.Core.Color.rock100       // SwiftUI.Color("coreRock100", bundle: .colors)
DesignTokens.Core.Color.rock200       // SwiftUI.Color("coreRock200", bundle: .colors)
DesignTokens.Core.Color.rock300       // SwiftUI.Color("coreRock300", bundle: .colors)
DesignTokens.Core.Color.rock400       // SwiftUI.Color("coreRock400", bundle: .colors)
DesignTokens.Core.Color.rock500       // SwiftUI.Color("coreRock500", bundle: .colors)
DesignTokens.Core.Color.rock600       // SwiftUI.Color("coreRock600", bundle: .colors)
DesignTokens.Core.Color.rock700       // SwiftUI.Color("coreRock700", bundle: .colors)
DesignTokens.Core.Color.rock800       // SwiftUI.Color("coreRock800", bundle: .colors)
DesignTokens.Core.Color.rock900       // SwiftUI.Color("coreRock900", bundle: .colors)
```

### Yellow Family (8 colors)
```swift
DesignTokens.Core.Color.yellow050     // SwiftUI.Color("coreYellow050", bundle: .colors)
DesignTokens.Core.Color.yellow100     // SwiftUI.Color("coreYellow100", bundle: .colors)
DesignTokens.Core.Color.yellow200     // SwiftUI.Color("coreYellow200", bundle: .colors)
DesignTokens.Core.Color.yellow300     // SwiftUI.Color("coreYellow300", bundle: .colors)
DesignTokens.Core.Color.yellow400     // SwiftUI.Color("coreYellow400", bundle: .colors)
DesignTokens.Core.Color.yellow500     // SwiftUI.Color("coreYellow500", bundle: .colors)
DesignTokens.Core.Color.yellow600     // SwiftUI.Color("coreYellow600", bundle: .colors)
DesignTokens.Core.Color.yellow700     // SwiftUI.Color("coreYellow700", bundle: .colors)
```

### Usage Example
```swift
Text("Hello World")
  .foregroundColor(DesignTokens.Core.Color.blue500)
  .background(DesignTokens.Core.Color.grey050)
```

---

## 5. Font Family (7 families)

```swift
DesignTokens.Core.FontFamily.decathlon      // "Decathlon VF"
DesignTokens.Core.FontFamily.decathlonBrand // "Decathlon Brand VF"
DesignTokens.Core.FontFamily.inter          // "Inter"
DesignTokens.Core.FontFamily.mono           // "monospace"
DesignTokens.Core.FontFamily.roboto         // "Roboto"
DesignTokens.Core.FontFamily.sansSerif      // "sans-serif"
DesignTokens.Core.FontFamily.sfPro          // "SF Pro"
```

### Usage Example
```swift
Text("Sample Text")
  .font(.custom(DesignTokens.Core.FontFamily.decathlon.rawValue, size: 16))
```

---

## 6. Font Size (20 sizes)

```swift
DesignTokens.Core.FontSize._050   // 11px
DesignTokens.Core.FontSize._100   // 12px
DesignTokens.Core.FontSize._150   // 14px
DesignTokens.Core.FontSize._200   // 16px
DesignTokens.Core.FontSize._250   // 18px
DesignTokens.Core.FontSize._300   // 20px
DesignTokens.Core.FontSize._350   // 22px
DesignTokens.Core.FontSize._400   // 24px
DesignTokens.Core.FontSize._450   // 26px
DesignTokens.Core.FontSize._500   // 28px
DesignTokens.Core.FontSize._550   // 30px
DesignTokens.Core.FontSize._600   // 32px
DesignTokens.Core.FontSize._650   // 34px
DesignTokens.Core.FontSize._700   // 36px
DesignTokens.Core.FontSize._750   // 40px
DesignTokens.Core.FontSize._800   // 48px
DesignTokens.Core.FontSize._850   // 56px
DesignTokens.Core.FontSize._900   // 72px
DesignTokens.Core.FontSize._910   // 74px
DesignTokens.Core.FontSize._950   // 88px
```

### Usage Example
```swift
Text("Title")
  .font(.system(size: CGFloat(DesignTokens.Core.FontSize._400)))
```

---

## 7. Font Weight (10 weights)

```swift
DesignTokens.Core.FontWeight._100   // 100 (Thin)
DesignTokens.Core.FontWeight._200   // 200 (Extra Light)
DesignTokens.Core.FontWeight._300   // 300 (Light)
DesignTokens.Core.FontWeight._400   // 400 (Regular)
DesignTokens.Core.FontWeight._500   // 500 (Medium)
DesignTokens.Core.FontWeight._600   // 600 (Semi Bold)
DesignTokens.Core.FontWeight._700   // 700 (Bold)
DesignTokens.Core.FontWeight._800   // 800 (Extra Bold)
DesignTokens.Core.FontWeight._900   // 900 (Black)
DesignTokens.Core.FontWeight._950   // 950 (Extra Black)
```

### Usage Example
```swift
Text("Bold Text")
  .fontWeight(Font.Weight(rawValue: Int(DesignTokens.Core.FontWeight._700)))
```

---

## 8. Letter Spacing (5 values)

```swift
DesignTokens.Core.LetterSpacing._020   // -3px (tight)
DesignTokens.Core.LetterSpacing._050   // -1px (slightly tight)
DesignTokens.Core.LetterSpacing._100   // 0px (normal)
DesignTokens.Core.LetterSpacing._200   // 1px (slightly loose)
DesignTokens.Core.LetterSpacing._300   // 2px (loose)
```

### Usage Example
```swift
Text("Spaced Text")
  .kerning(CGFloat(DesignTokens.Core.LetterSpacing._200))
```

---

## 9. Line Height (16 values)

```swift
DesignTokens.Core.LineHeight._050   // 100.0%
DesignTokens.Core.LineHeight._100   // 107.14%
DesignTokens.Core.LineHeight._110   // 108.11%
DesignTokens.Core.LineHeight._120   // 108.33%
DesignTokens.Core.LineHeight._200   // 110.0%
DesignTokens.Core.LineHeight._210   // 111.11%
DesignTokens.Core.LineHeight._260   // 116.67%
DesignTokens.Core.LineHeight._280   // 118.18%
DesignTokens.Core.LineHeight._285   // 118.75%
DesignTokens.Core.LineHeight._300   // 123.08%
DesignTokens.Core.LineHeight._380   // 128.57%
DesignTokens.Core.LineHeight._400   // 130.0%
DesignTokens.Core.LineHeight._430   // 133.33%
DesignTokens.Core.LineHeight._500   // 142.86%
DesignTokens.Core.LineHeight._800   // 150.0%
DesignTokens.Core.LineHeight._900   // 170.0%
```

### Usage Example
```swift
Text("Multi-line text with custom line height")
  .lineSpacing(CGFloat(DesignTokens.Core.LineHeight._800 - 100.0) / 100.0 * 16)
```

---

## 10. Opacity (6 levels)

```swift
DesignTokens.Core.Opacity._0      // 0% (transparent)
DesignTokens.Core.Opacity._250    // 25%
DesignTokens.Core.Opacity._300    // 38%
DesignTokens.Core.Opacity._500    // 50%
DesignTokens.Core.Opacity._750    // 75%
DesignTokens.Core.Opacity._1000   // 100% (opaque)
```

### Usage Example
```swift
Rectangle()
  .fill(Color.blue)
  .opacity(Double(DesignTokens.Core.Opacity._500) / 100.0)
```

---

## 11. Optical Sizing (3 options)

```swift
DesignTokens.Core.OpticalSizing._14   // "14"
DesignTokens.Core.OpticalSizing._30   // "30"
DesignTokens.Core.OpticalSizing.auto  // "auto"
```

### Usage Example
```swift
// Used internally by FontConstructor for typography tokens
```

---

## 12. Sizing (17 sizes)

```swift
DesignTokens.Core.Sizing._050   // 1px
DesignTokens.Core.Sizing._100   // 2px
DesignTokens.Core.Sizing._150   // 4px
DesignTokens.Core.Sizing._200   // 8px
DesignTokens.Core.Sizing._250   // 12px
DesignTokens.Core.Sizing._300   // 16px
DesignTokens.Core.Sizing._325   // 20px
DesignTokens.Core.Sizing._350   // 24px
DesignTokens.Core.Sizing._400   // 32px
DesignTokens.Core.Sizing._450   // 40px
DesignTokens.Core.Sizing._475   // 44px (accessibility touch size)
DesignTokens.Core.Sizing._500   // 48px
DesignTokens.Core.Sizing._520   // 56px
DesignTokens.Core.Sizing._550   // 64px
DesignTokens.Core.Sizing._600   // 80px
DesignTokens.Core.Sizing._650   // 96px
DesignTokens.Core.Sizing._700   // 128px
```

### Usage Example
```swift
Button("Touch Target") { }
  .frame(minWidth: CGFloat(DesignTokens.Core.Sizing._475),
         minHeight: CGFloat(DesignTokens.Core.Sizing._475))
```

---

## 13. Spacing (16 values)

```swift
DesignTokens.Core.Spacing._0     // 0px
DesignTokens.Core.Spacing._010   // 1px
DesignTokens.Core.Spacing._025   // 2px
DesignTokens.Core.Spacing._050   // 4px
DesignTokens.Core.Spacing._075   // 6px
DesignTokens.Core.Spacing._100   // 8px
DesignTokens.Core.Spacing._150   // 12px
DesignTokens.Core.Spacing._200   // 16px
DesignTokens.Core.Spacing._250   // 20px
DesignTokens.Core.Spacing._300   // 24px
DesignTokens.Core.Spacing._400   // 32px
DesignTokens.Core.Spacing._500   // 40px
DesignTokens.Core.Spacing._600   // 48px
DesignTokens.Core.Spacing._650   // 64px
DesignTokens.Core.Spacing._700   // 80px
DesignTokens.Core.Spacing._800   // 96px
DesignTokens.Core.Spacing._850   // 128px
DesignTokens.Core.Spacing._900   // 160px
```

### Usage Example
```swift
VStack(spacing: CGFloat(DesignTokens.Core.Spacing._200)) {
  Text("Item 1")
  Text("Item 2")
}
.padding(CGFloat(DesignTokens.Core.Spacing._300))
```

---

## 14. Text Case (4 options)

```swift
DesignTokens.Core.TextCase.capitalize  // "capitalize"
DesignTokens.Core.TextCase.lowercase   // "lowercase"
DesignTokens.Core.TextCase.none        // "none"
DesignTokens.Core.TextCase.uppercase   // "uppercase"
```

### Usage Example
```swift
Text("sample text")
  .textCase(.uppercase) // Uses SwiftUI's textCase modifier
```

---

## 15. Text Decoration (3 options)

```swift
DesignTokens.Core.TextDecoration.none         // "none"
DesignTokens.Core.TextDecoration.underline    // "underline"
DesignTokens.Core.TextDecoration.lineThrough  // "line-through"
```

### Usage Example
```swift
Text("Underlined text")
  .underline() // Uses SwiftUI's underline modifier
```

---

## Summary Statistics

**Total Foundation Core Design Tokens: 15 categories**

- **AspectRatio**: 16 ratios
- **BorderRadius**: 9 values
- **BorderWidth**: 4 values
- **Color**: 113 colors (12 families)
- **FontFamily**: 7 families
- **FontSize**: 20 sizes
- **FontWeight**: 10 weights
- **LetterSpacing**: 5 values
- **LineHeight**: 16 values
- **Opacity**: 6 levels
- **OpticalSizing**: 3 options
- **Sizing**: 17 sizes
- **Spacing**: 16 values
- **TextCase**: 4 options
- **TextDecoration**: 3 options

**Total Individual Tokens: 241 design tokens**

### Key Notes
- All tokens are accessed via: `DesignTokens.Core.[Category].[Token]`
- All color tokens automatically adapt to the current theme configuration
- All sizing and spacing values are in pixels (px)
- All opacity values are percentages (%)
- All line height values are percentages (%)

---

*End of Foundation Core Reference*
